{"version": 3, "sources": ["p__user__login__index-async.2485812050730738212.hot-update.js", "src/services/subscription.ts", "src/pages/team-management/components/TeamMemberManagement.tsx", "src/pages/user/login/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__user__login__index',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='46133676004068872';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 订阅管理相关 API 服务\n */\n\nimport type {\n  CreateSubscriptionRequest,\n  SubscriptionPlanResponse,\n  SubscriptionResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 订阅服务类\n */\nexport class SubscriptionService {\n  /**\n   * 获取所有订阅套餐（公开接口）\n   */\n  static async getAllPlans(): Promise<SubscriptionPlanResponse[]> {\n    const response = await apiRequest.get<SubscriptionPlanResponse[]>('/plans');\n    return response.data;\n  }\n\n  /**\n   * 获取活跃的订阅套餐\n   */\n  static async getActivePlans(): Promise<SubscriptionPlanResponse[]> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    return allPlans.filter((plan) => plan.isActive);\n  }\n\n  /**\n   * 根据 ID 获取订阅套餐详情\n   */\n  static async getPlanById(planId: number): Promise<SubscriptionPlanResponse> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    const plan = allPlans.find((p) => p.id === planId);\n\n    if (!plan) {\n      throw new Error('订阅套餐不存在');\n    }\n\n    return plan;\n  }\n\n  /**\n   * 获取当前用户的有效订阅\n   */\n  static async getCurrentSubscription(): Promise<SubscriptionResponse | null> {\n    try {\n      const response = await apiRequest.get<SubscriptionResponse>(\n        '/subscriptions/current',\n      );\n      return response.data;\n    } catch (error: any) {\n      // 如果没有订阅，返回 null\n      if (error?.response?.status === 404) {\n        return null;\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 创建订阅\n   */\n  static async createSubscription(\n    data: CreateSubscriptionRequest,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      '/subscriptions',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取用户订阅列表\n   */\n  static async getUserSubscriptions(): Promise<SubscriptionResponse[]> {\n    const response =\n      await apiRequest.get<SubscriptionResponse[]>('/subscriptions');\n    return response.data;\n  }\n\n  /**\n   * 取消订阅\n   */\n  static async cancelSubscription(subscriptionId: number): Promise<void> {\n    const response = await apiRequest.post<void>(\n      `/subscriptions/${subscriptionId}/cancel`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 续费订阅\n   */\n  static async renewSubscription(\n    subscriptionId: number,\n    duration: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/renew`,\n      { duration },\n    );\n    return response.data;\n  }\n\n  /**\n   * 升级订阅套餐\n   */\n  static async upgradeSubscription(\n    subscriptionId: number,\n    newPlanId: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/upgrade`,\n      { planId: newPlanId },\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取订阅使用统计\n   */\n  static async getSubscriptionUsage(): Promise<{\n    currentUsage: number;\n    maxUsage: number;\n    usagePercentage: number;\n    remainingDays: number;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        currentUsage: 0,\n        maxUsage: 0,\n        usagePercentage: 0,\n        remainingDays: 0,\n      };\n    }\n\n    // 计算剩余天数\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const remainingDays = Math.max(\n      0,\n      Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)),\n    );\n\n    // 这里需要后端提供实际的使用量数据\n    // 暂时返回模拟数据\n    const currentUsage = 0; // 实际使用量\n    const maxUsage = currentSubscription.maxSize;\n    const usagePercentage = maxUsage > 0 ? (currentUsage / maxUsage) * 100 : 0;\n\n    return {\n      currentUsage,\n      maxUsage,\n      usagePercentage,\n      remainingDays,\n    };\n  }\n\n  /**\n   * 获取订阅历史记录\n   */\n  static async getSubscriptionHistory(): Promise<SubscriptionResponse[]> {\n    const subscriptions = await SubscriptionService.getUserSubscriptions();\n\n    // 按创建时间倒序排列\n    return subscriptions.sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),\n    );\n  }\n\n  /**\n   * 计算套餐价格（考虑折扣等）\n   */\n  static calculatePlanPrice(\n    plan: SubscriptionPlanResponse,\n    duration: number,\n  ): {\n    originalPrice: number;\n    discountedPrice: number;\n    discount: number;\n    totalPrice: number;\n  } {\n    const originalPrice = plan.price * duration;\n    let discount = 0;\n\n    // 根据订阅时长给予折扣\n    if (duration >= 12) {\n      discount = 0.2; // 年付8折\n    } else if (duration >= 6) {\n      discount = 0.1; // 半年付9折\n    }\n\n    const discountedPrice = originalPrice * (1 - discount);\n\n    return {\n      originalPrice,\n      discountedPrice,\n      discount: discount * 100,\n      totalPrice: discountedPrice,\n    };\n  }\n\n  /**\n   * 比较套餐功能\n   */\n  static comparePlans(plans: SubscriptionPlanResponse[]): Array<{\n    feature: string;\n    values: Array<string | number | boolean>;\n  }> {\n    return [\n      {\n        feature: '数据存储上限',\n        values: plans.map((plan) => plan.maxSize),\n      },\n      {\n        feature: '月费价格',\n        values: plans.map((plan) => `¥${plan.price}`),\n      },\n      {\n        feature: '技术支持',\n        values: plans.map((plan) => (plan.price > 0 ? '7x24小时' : '工作日')),\n      },\n      {\n        feature: '数据备份',\n        values: plans.map((plan) => plan.price > 0),\n      },\n      {\n        feature: '高级功能',\n        values: plans.map((plan) => plan.price >= 100),\n      },\n    ];\n  }\n\n  /**\n   * 检查订阅状态\n   */\n  static async checkSubscriptionStatus(): Promise<{\n    hasActiveSubscription: boolean;\n    isExpiringSoon: boolean;\n    daysUntilExpiry: number;\n    needsUpgrade: boolean;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        hasActiveSubscription: false,\n        isExpiringSoon: false,\n        daysUntilExpiry: 0,\n        needsUpgrade: false,\n      };\n    }\n\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const daysUntilExpiry = Math.ceil(\n      (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),\n    );\n    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n\n    // 检查是否需要升级（基于使用量）\n    const usage = await SubscriptionService.getSubscriptionUsage();\n    const needsUpgrade = usage.usagePercentage > 80;\n\n    return {\n      hasActiveSubscription: true,\n      isExpiringSoon,\n      daysUntilExpiry,\n      needsUpgrade,\n    };\n  }\n}\n\n// 导出默认实例\nexport default SubscriptionService;\n", "/**\n * 团队成员与邀请管理组件\n *\n * 功能特性：\n * - 统一页面显示团队成员列表和邀请记录\n * - 查看团队成员列表及详细信息\n * - 查看团队邀请列表及状态管理\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 取消待处理的邀请\n * - 批量操作支持\n * - 成员和邀请搜索筛选\n *\n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n *\n * 界面设计：\n * - 移除标签页导航，采用统一页面布局\n * - 团队成员和邀请记录垂直排列\n * - 提升用户体验和操作效率\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Typography,\n  Popconfirm,\n  Select,\n  Tooltip\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { TeamDetailResponse, TeamMemberResponse, TeamInvitationResponse } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  // 邀请管理相关状态\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [invitationLoading, setInvitationLoading] = useState(false);\n  const [invitationSearchText, setInvitationSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  useEffect(() => {\n    fetchMembers();\n    fetchInvitations();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setInvitationLoading(true);\n      const invitationList = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setInvitationLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      // 使用新的邀请链接功能\n      const response = await InvitationService.sendInvitations({\n        emails: emailList,\n        message: values.message\n      });\n\n      // 显示详细的发送结果\n      if (response.successCount > 0) {\n        message.success(`成功发送 ${response.successCount} 个邀请`);\n\n        // 显示邀请链接（可选：在开发环境中显示）\n        if (process.env.NODE_ENV === 'development') {\n          console.log('邀请链接:', response.invitations.map(inv => ({\n            email: inv.email,\n            link: inv.invitationLink\n          })));\n        }\n      }\n\n      if (response.failureCount > 0) {\n        message.warning(`${response.failureCount} 个邀请发送失败`);\n      }\n\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      fetchInvitations(); // 刷新邀请列表\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请取消成功');\n      fetchInvitations();\n      onRefresh();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  // 邀请表格列定义\n  const invitationColumns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '被邀请人',\n      key: 'invitee',\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.inviteeName || '未注册用户'}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            <MailOutlined /> {record.inviteeEmail}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent\n          status={status}\n          isExpired={record.isExpired}\n        />\n      ),\n      filters: [\n        { text: '待确认', value: InvitationStatus.PENDING },\n        { text: '已接受', value: InvitationStatus.ACCEPTED },\n        { text: '已拒绝', value: InvitationStatus.REJECTED },\n        { text: '已过期', value: InvitationStatus.EXPIRED },\n        { text: '已取消', value: InvitationStatus.CANCELLED },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => {\n        if (record.status === InvitationStatus.PENDING && !record.isExpired) {\n          return (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                取消邀请\n              </Button>\n            </Popconfirm>\n          );\n        }\n        return <Text type=\"secondary\">-</Text>;\n      },\n    },\n  ];\n\n  // 过滤邀请列表\n  const filteredInvitations = invitations.filter(invitation => {\n    const matchesSearch = !invitationSearchText ||\n      invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) ||\n      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase()));\n\n    const matchesStatus = !statusFilter || invitation.status === statusFilter;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div>\n      {/* 团队成员管理区域 */}\n      <Card\n        title={\n          <Space>\n            <UserOutlined />\n            <span>团队成员 ({(members || []).length})</span>\n          </Space>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {/* 成员操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 250 }}\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button danger icon={<DeleteOutlined />}>\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Space>\n        </div>\n\n        {/* 成员列表 */}\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请记录管理区域 */}\n      <Card\n        title={\n          <Space>\n            <MailOutlined />\n            <span>邀请记录 ({(invitations || []).length})</span>\n          </Space>\n        }\n      >\n        {/* 邀请操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索邮箱或姓名\"\n                prefix={<SearchOutlined />}\n                value={invitationSearchText}\n                onChange={(e) => setInvitationSearchText(e.target.value)}\n                style={{ width: 200 }}\n              />\n              <Select\n                placeholder=\"筛选状态\"\n                value={statusFilter}\n                onChange={setStatusFilter}\n                style={{ width: 120 }}\n                allowClear\n              >\n                <Select.Option value={InvitationStatus.PENDING}>待确认</Select.Option>\n                <Select.Option value={InvitationStatus.ACCEPTED}>已接受</Select.Option>\n                <Select.Option value={InvitationStatus.REJECTED}>已拒绝</Select.Option>\n                <Select.Option value={InvitationStatus.EXPIRED}>已过期</Select.Option>\n                <Select.Option value={InvitationStatus.CANCELLED}>已取消</Select.Option>\n              </Select>\n            </Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchInvitations}\n              loading={invitationLoading}\n            >\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 邀请列表 */}\n        <Table\n          columns={invitationColumns}\n          dataSource={filteredInvitations}\n          rowKey=\"id\"\n          loading={invitationLoading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条邀请记录`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n            extra=\"您可以添加一些邀请消息，让被邀请人更好地了解邀请意图\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"欢迎加入我们的团队！我们期待与您一起工作...\"\n              maxLength={500}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { MailOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useCallback, useMemo, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\n// 登录表单组件（移到外部避免重新创建）\nconst LoginFormComponent: React.FC<{\n  form: any;\n  handleLogin: (values: LoginRequest) => void;\n  handleSendCode: () => void;\n  sendingCode: boolean;\n  countdown: number;\n  loading: boolean;\n}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {\n  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染\n  const sendCodeButton = useMemo(() => (\n    <Button\n      type=\"link\"\n      size=\"small\"\n      disabled={countdown > 0 || sendingCode}\n      loading={sendingCode}\n      onClick={handleSendCode}\n      style={{ padding: 0, height: 'auto' }}\n    >\n      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}\n    </Button>\n  ), [countdown, sendingCode, handleSendCode]);\n\n  // 使用 useMemo 稳定邮箱输入框，避免重新渲染\n  const emailField = useMemo(() => (\n    <Form.Item\n      key=\"email-field\"\n      name=\"email\"\n      rules={[\n        { required: true, message: '请输入邮箱！' },\n        { type: 'email', message: '请输入有效的邮箱地址！' },\n      ]}\n    >\n      <Input\n        key=\"email-input\"\n        prefix={<MailOutlined />}\n        placeholder=\"邮箱\"\n        autoComplete=\"email\"\n      />\n    </Form.Item>\n  ), []);\n\n  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染\n  const codeField = useMemo(() => (\n    <Form.Item\n      key=\"code-field\"\n      name=\"code\"\n      rules={[\n        { required: true, message: '请输入验证码！' },\n        { len: 6, message: '验证码为6位数字！' },\n        { pattern: /^\\d{6}$/, message: '验证码只能包含数字！' },\n      ]}\n    >\n      <Input\n        key=\"code-input\"\n        prefix={<SafetyOutlined />}\n        placeholder=\"6位验证码\"\n        maxLength={6}\n        suffix={sendCodeButton}\n      />\n    </Form.Item>\n  ), [sendCodeButton]);\n\n  return (\n    <Form\n      form={form}\n      name=\"login\"\n      size=\"large\"\n      onFinish={handleLogin}\n      autoComplete=\"off\"\n    >\n      {emailField}\n      {codeField}\n\n      {/* 提示信息 */}\n      <div style={{ marginBottom: 16, textAlign: 'center' }}>\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n          新用户将自动完成注册并登录\n        </Text>\n      </div>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录 / 注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n});\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [sendingCode, setSendingCode] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [form] = Form.useForm(); // 将表单实例提升到父组件\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 使用 Form 内置的邮箱验证\n\n  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入\n  useEffect(() => {\n    setCountdown(0);\n  }, []);\n\n  // 倒计时效果\n  React.useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (countdown > 0) {\n      timer = setTimeout(() => {\n        setCountdown(countdown - 1);\n      }, 1000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [countdown]);\n\n  // 发送验证码\n  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {\n    let email: string;\n\n    try {\n      // 验证邮箱字段\n      await form.validateFields(['email']);\n\n      // 从表单获取邮箱值\n      email = form.getFieldValue('email');\n      console.log('发送验证码前的邮箱值:', email);\n\n      if (!email) {\n        message.error('请输入邮箱地址');\n        return;\n      }\n    } catch (error) {\n      // 表单验证失败\n      message.error('请输入有效的邮箱地址');\n      return;\n    }\n\n    setSendingCode(true);\n    try {\n      const request: SendVerificationCodeRequest = { email, type };\n      const response = await AuthService.sendVerificationCode(request);\n\n      if (response.success) {\n        message.success(response.message);\n        setCountdown(60); // 60秒倒计时\n\n        // 验证码发送成功后检查表单值\n        console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));\n\n        // 在开发环境中提示查看控制台\n        if (process.env.NODE_ENV === 'development') {\n          message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);\n        }\n      } else {\n        message.error(response.message);\n        if (response.nextSendTime) {\n          setCountdown(response.nextSendTime);\n        }\n      }\n    } catch (error) {\n      console.error('发送验证码失败:', error);\n      message.error('发送验证码失败，请稍后重试');\n    } finally {\n      setSendingCode(false);\n    }\n  }, [form]);\n\n  // 处理登录/注册\n  const handleLogin = useCallback(async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n      message.success('登录成功！');\n\n      // 登录成功后停止倒计时\n      setCountdown(0);\n\n      // 登录成功后，刷新 initialState\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 等待一小段时间确保状态更新完成\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n      // 根据团队数量进行不同的跳转处理\n      if (response.teams.length === 0) {\n        // 没有团队，跳转到个人中心页面\n        history.push('/personal-center');\n      } else {\n        // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n        history.push('/personal-center', { teams: response.teams });\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [setInitialState]);\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录 / 注册\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <LoginFormComponent\n            form={form}\n            handleLogin={handleLogin}\n            handleSendCode={() => handleSendCode('login')}\n            sendingCode={sendingCode}\n            countdown={countdown}\n            loading={loading}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,yBACA;IACE,SAAS;;;;;;;;;;;;;gBCWA,mBAAmB;2BAAnB;;gBA8Qb,OAAmC;2BAAnC;;;;;4CAnR2B;;;;;;;;;YAKpB,MAAM;gBAIX,aAAa,cAAmD;oBAC9D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA6B;oBAClE,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,iBAAsD;oBACjE,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,OAAO,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;gBAChD;gBAKA,aAAa,YAAY,MAAc,EAAqC;oBAC1E,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,MAAM,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAE3C,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;oBAGlB,OAAO;gBACT;gBAKA,aAAa,yBAA+D;oBAC1E,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;wBAEF,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAO,OAAY;4BAEf;wBAAJ,IAAI,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,MAAK,KAC9B,OAAO;wBAET,MAAM;oBACR;gBACF;gBAKA,aAAa,mBACX,IAA+B,EACA;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,uBAAwD;oBACnE,MAAM,WACJ,MAAM,mBAAU,CAAC,GAAG,CAAyB;oBAC/C,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,mBAAmB,cAAsB,EAAiB;oBACrE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,OAAO,CAAC;oBAE3C,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,kBACX,cAAsB,EACtB,QAAgB,EACe;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,EACxC;wBAAE;oBAAS;oBAEb,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,oBACX,cAAsB,EACtB,SAAiB,EACc;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,QAAQ,CAAC,EAC1C;wBAAE,QAAQ;oBAAU;oBAEtB,OAAO,SAAS,IAAI;gBACtB;gBAKA,aAAa,uBAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,cAAc;wBACd,UAAU;wBACV,iBAAiB;wBACjB,eAAe;oBACjB;oBAIF,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,gBAAgB,KAAK,GAAG,CAC5B,GACA,KAAK,IAAI,CAAC,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAKnD,MAAM,eAAe;oBACrB,MAAM,WAAW,oBAAoB,OAAO;oBAC5C,MAAM,kBAAkB,WAAW,IAAI,AAAC,eAAe,WAAY,MAAM;oBAEzE,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF;gBAKA,aAAa,yBAA0D;oBACrE,MAAM,gBAAgB,MAAM,oBAAoB,oBAAoB;oBAGpE,OAAO,cAAc,IAAI,CACvB,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAErE;gBAKA,OAAO,mBACL,IAA8B,EAC9B,QAAgB,EAMhB;oBACA,MAAM,gBAAgB,KAAK,KAAK,GAAG;oBACnC,IAAI,WAAW;oBAGf,IAAI,YAAY,IACd,WAAW;yBACN,IAAI,YAAY,GACrB,WAAW;oBAGb,MAAM,kBAAkB,gBAAiB,CAAA,IAAI,QAAO;oBAEpD,OAAO;wBACL;wBACA;wBACA,UAAU,WAAW;wBACrB,YAAY;oBACd;gBACF;gBAKA,OAAO,aAAa,KAAiC,EAGlD;oBACD,OAAO;wBACL;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;wBAC1C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;wBAC9C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAU,KAAK,KAAK,GAAG,IAAI,WAAW;wBAC3D;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,GAAG;wBAC3C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,IAAI;wBAC5C;qBACD;gBACH;gBAKA,aAAa,0BAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,uBAAuB;wBACvB,gBAAgB;wBAChB,iBAAiB;wBACjB,cAAc;oBAChB;oBAGF,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,kBAAkB,KAAK,IAAI,CAC/B,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAEzC,MAAM,iBAAiB,mBAAmB,KAAK,kBAAkB;oBAGjE,MAAM,QAAQ,MAAM,oBAAoB,oBAAoB;oBAC5D,MAAM,eAAe,MAAM,eAAe,GAAG;oBAE7C,OAAO;wBACL,uBAAuB;wBACvB;wBACA;wBACA;oBACF;gBACF;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC+Tf;;;2BAAA;;;;;;;sEAnkB2C;yCAepC;0CASA;qEAEW;yCAGU;+CACM;wCAED;gFACK;;;;;;;;;;YAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;gBACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;gBAGjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;gBAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;gBAC3D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;gBAEzD,IAAA,gBAAS,EAAC;oBACR;oBACA;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE;oBACf,SAAU;wBACR,WAAW;oBACb;gBACF;gBAGA,MAAM,mBAAmB;oBACvB,IAAI;wBACF,qBAAqB;wBACrB,MAAM,iBAAiB,MAAM,6BAAiB,CAAC,yBAAyB;wBACxE,eAAe,kBAAkB,EAAE;oBACrC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,eAAe,EAAE;oBACnB,SAAU;wBACR,qBAAqB;oBACvB;gBACF;gBAGA,MAAM,sBAAsB,OAAO;oBACjC,IAAI;wBACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;wBAGnB,MAAM,WAAW,MAAM,6BAAiB,CAAC,eAAe,CAAC;4BACvD,QAAQ;4BACR,SAAS,OAAO,OAAO;wBACzB;wBAGA,IAAI,SAAS,YAAY,GAAG,GAAG;4BAC7B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,YAAY,CAAC,IAAI,CAAC;4BAIjD,QAAQ,GAAG,CAAC,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;oCACpD,OAAO,IAAI,KAAK;oCAChB,MAAM,IAAI,cAAc;gCAC1B,CAAA;wBAEJ;wBAEA,IAAI,SAAS,YAAY,GAAG,GAC1B,aAAO,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,QAAQ,CAAC;wBAGpD,sBAAsB;wBACtB,WAAW,WAAW;wBACtB;wBACA;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,6BAAiB,CAAC,gBAAgB,CAAC;wBACzC,aAAO,CAAC,OAAO,CAAC;wBAChB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;wBACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;wBACtC;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,oBAAoB;oBACxB,IAAI;wBACF,MAAM,YAAY;wBAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;wBAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC7C,mBAAmB,EAAE;wBACrB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAI5D,MAAM,2BAA2B,OAAO,QAA4B;oBAClE,IAAI;wBACF,MAAM,iBAAW,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;wBAChD,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;wBAC7D;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAGA,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAc,SACrB,2BAAC,WAAK;;oCACJ,2BAAC;wCAAK,MAAM;kDAAE;;;;;;oCACb,OAAO,SAAS,IACf,2BAAC,SAAG;wCAAC,MAAM,2BAAC,oBAAa;;;;;wCAAK,OAAM;kDAAO;;;;;;;;;;;;oBAInD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,QACP,2BAAC;gCAAK,MAAK;0CAAa;;;;;;oBAE5B;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,WACP,2BAAC,SAAG;gCAAC,OAAO,WAAW,UAAU;0CAC9B,WAAW,OAAO;;;;;;oBAGzB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,OACE,2BAAC,WAAK;;oCACH,OAAO,QAAQ,GACd,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;+CAID,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;;oCAIH,2BAAC,gBAAU;wCACT,OAAM;wCACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;wCAChD,WAAW,IAAM,mBAAmB;wCACpC,QAAO;wCACP,YAAW;wCACX,QAAO;kDAEP,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM;4CACN,MAAK;4CACL,MAAM,2BAAC,qBAAc;;;;;sDACtB;;;;;;;;;;;;;;;;;wBAMT;oBACF;iBACD;gBAGD,MAAM,eAAe;oBACnB;oBACA,UAAU;oBACV,kBAAkB,CAAC,SAAgC,CAAA;4BACjD,UAAU,OAAO,SAAS;wBAC5B,CAAA;gBACF;gBAGA,MAAM,oBAAyD;oBAC7D;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,SACV,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAM;;oCAChC,2BAAC;wCAAK,MAAM;kDAAE,OAAO,WAAW,IAAI;;;;;;oCACpC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAO;;4CAC/C,2BAAC,mBAAY;;;;;4CAAG;4CAAE,OAAO,YAAY;;;;;;;;;;;;;oBAI7C;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,QAAQ,SACf,2BAAC,yBAAyB;gCACxB,QAAQ;gCACR,WAAW,OAAO,SAAS;;;;;;wBAG/B,SAAS;4BACP;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,SAAS;4BAAC;yBAClD;wBACD,UAAU,CAAC,OAAO,SAAW,OAAO,MAAM,KAAK;oBACjD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,OACP,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;wBAGxB,QAAQ,CAAC,GAAG,IAAM,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI,KAAK,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI;oBACvE;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAM;4BACb,MAAM,YAAY,OAAO,SAAS;4BAClC,OACE,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CACjC,2BAAC;oCAAK,MAAM,YAAY,WAAW;8CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;;;;;;wBAI5B;oBACF;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,MAAM,KAAK,qBAAgB,CAAC,OAAO,IAAI,CAAC,OAAO,SAAS,EACjE,OACE,2BAAC,gBAAU;gCACT,OAAM;gCACN,WAAW,IAAM,uBAAuB,OAAO,EAAE;gCACjD,QAAO;gCACP,YAAW;0CAEX,2BAAC,YAAM;oCAAC,MAAK;oCAAQ,MAAM;oCAAC,MAAM,2BAAC,qBAAc;;;;;8CAAK;;;;;;;;;;;4BAM5D,OAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;wBAChC;oBACF;iBACD;gBAGD,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;oBAC7C,MAAM,gBAAgB,CAAC,wBACrB,WAAW,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW,OAC9E,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW;oBAE3G,MAAM,gBAAgB,CAAC,gBAAgB,WAAW,MAAM,KAAK;oBAE7D,OAAO,iBAAiB;gBAC1B;gBAEA,OACE,2BAAC;;wBAEC,2BAAC,UAAI;4BACH,OACE,2BAAC,WAAK;;oCACJ,2BAAC,mBAAY;;;;;oCACb,2BAAC;;4CAAK;4CAAQ,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAAC;;;;;;;;;;;;;4BAGxC,OAAO;gCAAE,cAAc;4BAAG;;gCAG1B,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,WAAK;wCAAC,OAAO;4CAAE,OAAO;4CAAQ,gBAAgB;wCAAgB;;4CAC7D,2BAAC,WAAK;;oDACJ,2BAAC,WAAK;wDACJ,aAAY;wDACZ,QAAQ,2BAAC,qBAAc;;;;;wDACvB,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,OAAO;4DAAE,OAAO;wDAAI;;;;;;oDAErB,gBAAgB,MAAM,GAAG,KACxB,2BAAC,gBAAU;wDACT,OAAO,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;wDACjD,WAAW;wDACX,QAAO;wDACP,YAAW;kEAEX,2BAAC,YAAM;4DAAC,MAAM;4DAAC,MAAM,2BAAC,qBAAc;;;;;;gEAAK;gEAChC,gBAAgB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;4CAKtC,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAM,2BAAC,sBAAe;;;;;gDACtB,SAAS,IAAM,sBAAsB;0DACtC;;;;;;;;;;;;;;;;;gCAOL,2BAAC,WAAK;oCACJ,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,cAAc;oCACd,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;wCACtC,UAAU;oCACZ;;;;;;;;;;;;wBAKJ,2BAAC,UAAI;4BACH,OACE,2BAAC,WAAK;;oCACJ,2BAAC,mBAAY;;;;;oCACb,2BAAC;;4CAAK;4CAAQ,CAAA,eAAe,EAAE,AAAD,EAAG,MAAM;4CAAC;;;;;;;;;;;;;;gCAK5C,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,WAAK;wCAAC,OAAO;4CAAE,OAAO;4CAAQ,gBAAgB;wCAAgB;;4CAC7D,2BAAC,WAAK;;oDACJ,2BAAC,WAAK;wDACJ,aAAY;wDACZ,QAAQ,2BAAC,qBAAc;;;;;wDACvB,OAAO;wDACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;wDACvD,OAAO;4DAAE,OAAO;wDAAI;;;;;;oDAEtB,2BAAC,YAAM;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU;wDACV,OAAO;4DAAE,OAAO;wDAAI;wDACpB,UAAU;;4DAEV,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,OAAO;0EAAE;;;;;;4DAChD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,QAAQ;0EAAE;;;;;;4DACjD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,QAAQ;0EAAE;;;;;;4DACjD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,OAAO;0EAAE;;;;;;4DAChD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,SAAS;0EAAE;;;;;;;;;;;;;;;;;;4CAGtD,2BAAC,YAAM;gDACL,MAAM,2BAAC,qBAAc;;;;;gDACrB,SAAS;gDACT,SAAS;0DACV;;;;;;;;;;;;;;;;;gCAOL,2BAAC,WAAK;oCACJ,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;wCACxC,UAAU;oCACZ;;;;;;;;;;;;wBAOJ,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,sBAAsB;gCACtB,WAAW,WAAW;4BACxB;4BACA,QAAQ;4BACR,OAAO;sCAEP,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;oCAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCACtC;wCACD,OAAM;kDAEN,2BAAC;4CACC,MAAM;4CACN,aAAY;;;;;;;;;;;oCAGhB,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAM;kDAEN,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,WAAW;4CACX,SAAS;;;;;;;;;;;oCAGb,2BAAC,UAAI,CAAC,IAAI;kDACR,2BAAC,WAAK;;gDACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,MAAM,2BAAC,mBAAY;;;;;8DAAK;;;;;;gDAGjE,2BAAC,YAAM;oDAAC,SAAS,IAAM,sBAAsB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASnE;eAthBM;;oBASiB,UAAI,CAAC;;;iBATtB;gBAwhBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCrRf;;;2BAAA;;;;;;;0CAjU6C;wCACH;yCASnC;8CACsB;sEACoC;+CAC1C;6CACK;+EAEP;;;;;;;;;;;YAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAGlC,MAAM,qBAOD,cAAK,CAAC,IAAI,IAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE;;gBAErF,MAAM,iBAAiB,IAAA,cAAO,EAAC,IAC7B,2BAAC,YAAM;wBACL,MAAK;wBACL,MAAK;wBACL,UAAU,YAAY,KAAK;wBAC3B,SAAS;wBACT,SAAS;wBACT,OAAO;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;kCAEnC,YAAY,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG;;;;;8BAEvC;oBAAC;oBAAW;oBAAa;iBAAe;gBAG3C,MAAM,aAAa,IAAA,cAAO,EAAC,IACzB,2BAAC,UAAI,CAAC,IAAI;wBAER,MAAK;wBACL,OAAO;4BACL;gCAAE,UAAU;gCAAM,SAAS;4BAAS;4BACpC;gCAAE,MAAM;gCAAS,SAAS;4BAAc;yBACzC;kCAED,2BAAC,WAAK;4BAEJ,QAAQ,2BAAC,mBAAY;;;;;4BACrB,aAAY;4BACZ,cAAa;2BAHT;;;;;uBARF;;;;8BAcL,EAAE;gBAGL,MAAM,YAAY,IAAA,cAAO,EAAC,IACxB,2BAAC,UAAI,CAAC,IAAI;wBAER,MAAK;wBACL,OAAO;4BACL;gCAAE,UAAU;gCAAM,SAAS;4BAAU;4BACrC;gCAAE,KAAK;gCAAG,SAAS;4BAAY;4BAC/B;gCAAE,SAAS;gCAAW,SAAS;4BAAa;yBAC7C;kCAED,2BAAC,WAAK;4BAEJ,QAAQ,2BAAC,qBAAc;;;;;4BACvB,aAAY;4BACZ,WAAW;4BACX,QAAQ;2BAJJ;;;;;uBATF;;;;8BAgBL;oBAAC;iBAAe;gBAEnB,OACE,2BAAC,UAAI;oBACH,MAAM;oBACN,MAAK;oBACL,MAAK;oBACL,UAAU;oBACV,cAAa;;wBAEZ;wBACA;wBAGD,2BAAC;4BAAI,OAAO;gCAAE,cAAc;gCAAI,WAAW;4BAAS;sCAClD,2BAAC;gCAAK,MAAK;gCAAY,OAAO;oCAAE,UAAU;gCAAO;0CAAG;;;;;;;;;;;wBAKtD,2BAAC,UAAI,CAAC,IAAI;sCACR,2BAAC,YAAM;gCAAC,MAAK;gCAAU,UAAS;gCAAS,SAAS;gCAAS,KAAK;0CAAC;;;;;;;;;;;;;;;;;YAMzE;iBAvFM;YAyFN,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;gBACvC,OAAO;oBACL,WAAW;wBACT,SAAS;wBACT,eAAe;wBACf,QAAQ;wBACR,UAAU;wBACV,iBACE;wBACF,gBAAgB;oBAClB;oBACA,SAAS;wBACP,MAAM;wBACN,SAAS;wBACT,eAAe;wBACf,gBAAgB;wBAChB,YAAY;wBACZ,SAAS;oBACX;oBACA,QAAQ;wBACN,cAAc;wBACd,WAAW;oBACb;oBACA,MAAM;wBACJ,cAAc;oBAChB;oBACA,OAAO;wBACL,cAAc;oBAChB;oBACA,WAAW;wBACT,OAAO;wBACP,UAAU;wBACV,WAAW,MAAM,iBAAiB;oBACpC;oBACA,QAAQ;wBACN,WAAW;wBACX,WAAW;oBACb;oBACA,MAAM;wBACJ,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,UAAU;wBACV,OAAO;wBACP,KAAK;wBACL,cAAc,MAAM,YAAY;wBAChC,UAAU;4BACR,iBAAiB,MAAM,gBAAgB;wBACzC;oBACF;gBACF;YACF;YAEA,MAAM,YAAsB;;gBAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAC3C,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;gBAC3B,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAKrC,IAAA,gBAAS,EAAC;oBACR,aAAa;gBACf,GAAG,EAAE;gBAGL,cAAK,CAAC,SAAS,CAAC;oBACd,IAAI;oBACJ,IAAI,YAAY,GACd,QAAQ,WAAW;wBACjB,aAAa,YAAY;oBAC3B,GAAG;oBAEL,OAAO;wBACL,IAAI,OAAO,aAAa;oBAC1B;gBACF,GAAG;oBAAC;iBAAU;gBAGd,MAAM,iBAAiB,IAAA,kBAAW,EAAC,OAAO,OAA6B,OAAO;oBAC5E,IAAI;oBAEJ,IAAI;wBAEF,MAAM,KAAK,cAAc,CAAC;4BAAC;yBAAQ;wBAGnC,QAAQ,KAAK,aAAa,CAAC;wBAC3B,QAAQ,GAAG,CAAC,eAAe;wBAE3B,IAAI,CAAC,OAAO;4BACV,aAAO,CAAC,KAAK,CAAC;4BACd;wBACF;oBACF,EAAE,OAAO,OAAO;wBAEd,aAAO,CAAC,KAAK,CAAC;wBACd;oBACF;oBAEA,eAAe;oBACf,IAAI;wBACF,MAAM,UAAuC;4BAAE;4BAAO;wBAAK;wBAC3D,MAAM,WAAW,MAAM,qBAAW,CAAC,oBAAoB,CAAC;wBAExD,IAAI,SAAS,OAAO,EAAE;4BACpB,aAAO,CAAC,OAAO,CAAC,SAAS,OAAO;4BAChC,aAAa;4BAGb,QAAQ,GAAG,CAAC,iBAAiB,KAAK,aAAa,CAAC;4BAI9C,aAAO,CAAC,IAAI,CAAC,4BAA4B;wBAE7C,OAAO;4BACL,aAAO,CAAC,KAAK,CAAC,SAAS,OAAO;4BAC9B,IAAI,SAAS,YAAY,EACvB,aAAa,SAAS,YAAY;wBAEtC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;wBAC1B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,eAAe;oBACjB;gBACF,GAAG;oBAAC;iBAAK;gBAGT,MAAM,cAAc,IAAA,kBAAW,EAAC,OAAO;oBACrC,WAAW;oBACX,IAAI;wBACF,MAAM,WAAW,MAAM,qBAAW,CAAC,KAAK,CAAC;wBACzC,aAAO,CAAC,OAAO,CAAC;wBAGhB,aAAa;wBAGb,MAAM,gBAAgB,CAAC,YAAe,CAAA;gCACpC,GAAG,SAAS;gCACZ,aAAa,SAAS,IAAI;gCAC1B,aAAa,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG;4BAC/D,CAAA;wBAGA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;wBAGjD,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAE5B,YAAO,CAAC,IAAI,CAAC;6BAGb,YAAO,CAAC,IAAI,CAAC,oBAAoB;4BAAE,OAAO,SAAS,KAAK;wBAAC;oBAE7D,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,SAAS;oBACzB,SAAU;wBACR,WAAW;oBACb;gBACF,GAAG;oBAAC;iBAAgB;gBAIpB,OACE,2BAAC;oBAAI,WAAW,OAAO,SAAS;;wBAC9B,2BAAC,WAAM;sCACL,2BAAC;;oCAAM;oCAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;wBAG7C,2BAAC;4BAAI,WAAW,OAAO,OAAO;;gCAC5B,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,2BAAC,WAAK;wCAAC,WAAU;wCAAW,OAAM;wCAAS,MAAK;;4CAC9C,2BAAC;gDAAI,WAAW,OAAO,IAAI;0DACzB,2BAAC;oDAAI,KAAI;oDAAY,KAAI;oDAAW,QAAQ;;;;;;;;;;;4CAE9C,2BAAC;gDAAI,WAAW,OAAO,KAAK;;oDAC1B,2BAAC;wDAAM,OAAO;kEAAG;;;;;;oDACjB,2BAAC;wDAAK,MAAK;kEAAY;;;;;;;;;;;;;;;;;;;;;;;gCAK7B,2BAAC,UAAI;oCAAC,WAAW,OAAO,SAAS;8CAC/B,2BAAC;wCACC,MAAM;wCACN,aAAa;wCACb,gBAAgB,IAAM,eAAe;wCACrC,aAAa;wCACb,WAAW;wCACX,SAAS;;;;;;;;;;;gCAIb,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;wBAG3B,2BAAC,kBAAM;;;;;;;;;;;YAGb;gBA5JM;;oBAIW,UAAI,CAAC;oBACD;oBACS,aAAQ;;;kBANhC;gBA8JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;IHnUD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}