globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
            var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
            var _UserProfileCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserProfileCard.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const PersonalCenterPage = ()=>{
                _s();
                const { initialState, loading } = (0, _max.useModel)('@@initialState');
                const [renderError, setRenderError] = (0, _react.useState)(null);
                // 调试信息
                console.log('PersonalCenterPage 渲染:', {
                    loading,
                    currentUser: initialState === null || initialState === void 0 ? void 0 : initialState.currentUser,
                    currentTeam: initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam
                });
                // 错误边界处理
                (0, _react.useEffect)(()=>{
                    const handleError = (error)=>{
                        console.error('页面渲染错误:', error);
                        setRenderError('页面加载出现错误，请刷新页面重试');
                    };
                    window.addEventListener('error', handleError);
                    return ()=>window.removeEventListener('error', handleError);
                }, []);
                // 如果正在加载，显示加载状态
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 43,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在加载用户信息..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 44,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 34,
                    columnNumber: 7
                }, this);
                // 如果用户未登录，跳转到登录页
                (0, _react.useEffect)(()=>{
                    if (!loading && !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) _max.history.push('/user/login');
                }, [
                    loading,
                    initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
                ]);
                // 如果有渲染错误，显示错误信息
                if (renderError) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '20px'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "页面加载失败",
                        description: renderError,
                        type: "error",
                        showIcon: true,
                        action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("button", {
                            onClick: ()=>window.location.reload(),
                            style: {
                                background: '#ff4d4f',
                                color: 'white',
                                border: 'none',
                                padding: '4px 12px',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            },
                            children: "刷新页面"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 59,
                    columnNumber: 7
                }, this);
                // 如果用户未登录，不渲染页面内容（避免闪烁）
                if (!loading && !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) return null;
                // 如果用户已登录但还在加载中，显示加载状态
                if ((initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) && loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 111,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在加载个人中心..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 112,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 102,
                    columnNumber: 7
                }, this);
                // 确保有用户信息才渲染页面内容
                if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 129,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在验证登录状态..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 130,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 120,
                    columnNumber: 7
                }, this);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                minHeight: '100vh',
                                background: '#f5f8ff',
                                padding: '12px 12px 24px 12px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                style: {
                                    width: '100%',
                                    minHeight: 'calc(100vh - 48px)',
                                    borderRadius: '12px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                                },
                                styles: {
                                    body: {
                                        padding: '24px'
                                    }
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: [
                                        16,
                                        16
                                    ],
                                    style: {
                                        margin: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            style: {
                                                marginBottom: 8
                                            },
                                            children: (()=>{
                                                try {
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileCard.default, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 163,
                                                        columnNumber: 24
                                                    }, this);
                                                } catch (error) {
                                                    console.error('UserProfileCard 渲染错误:', error);
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                        message: "个人信息加载失败",
                                                        description: "个人信息组件出现错误，请刷新页面重试",
                                                        type: "error",
                                                        showIcon: true
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 167,
                                                        columnNumber: 19
                                                    }, this);
                                                }
                                            })()
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 160,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            style: {
                                                marginBottom: 8
                                            },
                                            children: (()=>{
                                                try {
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 190,
                                                        columnNumber: 24
                                                    }, this);
                                                } catch (error) {
                                                    console.error('TodoManagement 渲染错误:', error);
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                        message: "待办事项加载失败",
                                                        description: "待办事项组件出现错误，请刷新页面重试",
                                                        type: "error",
                                                        showIcon: true
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 194,
                                                        columnNumber: 19
                                                    }, this);
                                                }
                                            })()
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 179,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            children: (()=>{
                                                try {
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 209,
                                                        columnNumber: 24
                                                    }, this);
                                                } catch (error) {
                                                    console.error('TeamListCard 渲染错误:', error);
                                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                        message: "团队列表加载失败",
                                                        description: "团队列表组件出现错误，请刷新页面重试",
                                                        type: "error",
                                                        showIcon: true
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/index.tsx",
                                                        lineNumber: 213,
                                                        columnNumber: 19
                                                    }, this);
                                                }
                                            })()
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 206,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 158,
                                    columnNumber: 9
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 145,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 137,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 228,
                            columnNumber: 5
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(PersonalCenterPage, "O8WUTYtY6ysFz/lchPjRMNeMpg0=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = PersonalCenterPage;
            var _default = PersonalCenterPage;
            var _c;
            $RefreshReg$(_c, "PersonalCenterPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '14642364649912977921';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.15758116565430761915.hot-update.js.map