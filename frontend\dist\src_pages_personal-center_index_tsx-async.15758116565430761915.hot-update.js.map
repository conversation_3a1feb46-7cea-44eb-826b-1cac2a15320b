{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.15758116565430761915.hot-update.js", "src/pages/personal-center/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='14642364649912977921';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "import { useModel, history } from '@umijs/max';\nimport { Card, Col, Row, Spin, Alert } from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport UserProfileCard from './UserProfileCard';\n\nconst PersonalCenterPage: React.FC = () => {\n  const { initialState, loading } = useModel('@@initialState');\n  const [renderError, setRenderError] = useState<string | null>(null);\n\n  // 调试信息\n  console.log('PersonalCenterPage 渲染:', {\n    loading,\n    currentUser: initialState?.currentUser,\n    currentTeam: initialState?.currentTeam,\n  });\n\n  // 错误边界处理\n  useEffect(() => {\n    const handleError = (error: ErrorEvent) => {\n      console.error('页面渲染错误:', error);\n      setRenderError('页面加载出现错误，请刷新页面重试');\n    };\n\n    window.addEventListener('error', handleError);\n    return () => window.removeEventListener('error', handleError);\n  }, []);\n\n  // 如果正在加载，显示加载状态\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  // 如果用户未登录，跳转到登录页\n  useEffect(() => {\n    if (!loading && !initialState?.currentUser) {\n      history.push('/user/login');\n    }\n  }, [loading, initialState?.currentUser]);\n\n  // 如果有渲染错误，显示错误信息\n  if (renderError) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: '20px',\n        }}\n      >\n        <Alert\n          message=\"页面加载失败\"\n          description={renderError}\n          type=\"error\"\n          showIcon\n          action={\n            <button\n              onClick={() => window.location.reload()}\n              style={{\n                background: '#ff4d4f',\n                color: 'white',\n                border: 'none',\n                padding: '4px 12px',\n                borderRadius: '4px',\n                cursor: 'pointer',\n              }}\n            >\n              刷新页面\n            </button>\n          }\n        />\n      </div>\n    );\n  }\n\n  // 如果用户未登录，不渲染页面内容（避免闪烁）\n  if (!loading && !initialState?.currentUser) {\n    return null;\n  }\n\n  // 如果用户已登录但还在加载中，显示加载状态\n  if (initialState?.currentUser && loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载个人中心...</div>\n      </div>\n    );\n  }\n\n  // 确保有用户信息才渲染页面内容\n  if (!initialState?.currentUser) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在验证登录状态...</div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          padding: '12px 12px 24px 12px', // 移动端减少左右边距\n        }}\n      >\n        {/* 大的容器区域 */}\n        <Card\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)',\n            borderRadius: '12px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n          }}\n          styles={{\n            body: {\n              padding: '24px',\n            },\n          }}\n        >\n        <Row gutter={[16, 16]} style={{ margin: 0 }}>\n          {/* 个人信息卡片 - 全宽显示 */}\n          <Col xs={24} style={{ marginBottom: 8 }}>\n            {(() => {\n              try {\n                return <UserProfileCard />;\n              } catch (error) {\n                console.error('UserProfileCard 渲染错误:', error);\n                return (\n                  <Alert\n                    message=\"个人信息加载失败\"\n                    description=\"个人信息组件出现错误，请刷新页面重试\"\n                    type=\"error\"\n                    showIcon\n                  />\n                );\n              }\n            })()}\n          </Col>\n\n          {/* 待办事项 - 响应式布局 */}\n          <Col\n            xs={24}\n            sm={24}\n            md={24}\n            lg={12}\n            xl={12}\n            xxl={12}\n            style={{ marginBottom: 8 }}\n          >\n            {(() => {\n              try {\n                return <TodoManagement />;\n              } catch (error) {\n                console.error('TodoManagement 渲染错误:', error);\n                return (\n                  <Alert\n                    message=\"待办事项加载失败\"\n                    description=\"待办事项组件出现错误，请刷新页面重试\"\n                    type=\"error\"\n                    showIcon\n                  />\n                );\n              }\n            })()}\n          </Col>\n\n          {/* 团队列表 - 响应式布局 */}\n          <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n            {(() => {\n              try {\n                return <TeamListCard />;\n              } catch (error) {\n                console.error('TeamListCard 渲染错误:', error);\n                return (\n                  <Alert\n                    message=\"团队列表加载失败\"\n                    description=\"团队列表组件出现错误，请刷新页面重试\"\n                    type=\"error\"\n                    showIcon\n                  />\n                );\n              }\n            })()}\n          </Col>\n        </Row>\n      </Card>\n    </div>\n\n    {/* 浮动按钮 */}\n    <UserFloatButton />\n  </>\n  );\n};\n\nexport default PersonalCenterPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCqOb;;;2BAAA;;;;;;;wCAxOkC;yCACU;oFACD;yFACf;0FACH;4FACE;6FACC;;;;;;;;;;YAE5B,MAAM,qBAA+B;;gBACnC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAgB;gBAE9D,OAAO;gBACP,QAAQ,GAAG,CAAC,0BAA0B;oBACpC;oBACA,WAAW,EAAE,yBAAA,mCAAA,aAAc,WAAW;oBACtC,WAAW,EAAE,yBAAA,mCAAA,aAAc,WAAW;gBACxC;gBAEA,SAAS;gBACT,IAAA,gBAAS,EAAC;oBACR,MAAM,cAAc,CAAC;wBACnB,QAAQ,KAAK,CAAC,WAAW;wBACzB,eAAe;oBACjB;oBAEA,OAAO,gBAAgB,CAAC,SAAS;oBACjC,OAAO,IAAM,OAAO,mBAAmB,CAAC,SAAS;gBACnD,GAAG,EAAE;gBAEL,gBAAgB;gBAChB,IAAI,SACF,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC,iBAAiB;gBACjB,IAAA,gBAAS,EAAC;oBACR,IAAI,CAAC,WAAW,EAAC,yBAAA,mCAAA,aAAc,WAAW,GACxC,YAAO,CAAC,IAAI,CAAC;gBAEjB,GAAG;oBAAC;oBAAS,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAEvC,iBAAiB;gBACjB,IAAI,aACF,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;wBACZ,SAAS;oBACX;8BAEA,cAAA,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,sBACE,2BAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,OAAO;gCACL,YAAY;gCACZ,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,cAAc;gCACd,QAAQ;4BACV;sCACD;;;;;;;;;;;;;;;;gBASX,wBAAwB;gBACxB,IAAI,CAAC,WAAW,EAAC,yBAAA,mCAAA,aAAc,WAAW,GACxC,OAAO;gBAGT,uBAAuB;gBACvB,IAAI,CAAA,yBAAA,mCAAA,aAAc,WAAW,KAAI,SAC/B,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC,iBAAiB;gBACjB,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC,qBACE;;sCACE,2BAAC;4BACC,OAAO;gCACL,WAAW;gCACX,YAAY;gCACZ,SAAS;4BACX;sCAGA,cAAA,2BAAC,UAAI;gCACH,OAAO;oCACL,OAAO;oCACP,WAAW;oCACX,cAAc;oCACd,WAAW;gCACb;gCACA,QAAQ;oCACN,MAAM;wCACJ,SAAS;oCACX;gCACF;0CAEF,cAAA,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;oCAAE,OAAO;wCAAE,QAAQ;oCAAE;;sDAExC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,OAAO;gDAAE,cAAc;4CAAE;sDACnC,AAAC,CAAA;gDACA,IAAI;oDACF,qBAAO,2BAAC,wBAAe;;;;;gDACzB,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,yBAAyB;oDACvC,qBACE,2BAAC,WAAK;wDACJ,SAAQ;wDACR,aAAY;wDACZ,MAAK;wDACL,QAAQ;;;;;;gDAGd;4CACF,CAAA;;;;;;sDAIF,2BAAC,SAAG;4CACF,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,KAAK;4CACL,OAAO;gDAAE,cAAc;4CAAE;sDAExB,AAAC,CAAA;gDACA,IAAI;oDACF,qBAAO,2BAAC,uBAAc;;;;;gDACxB,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,wBAAwB;oDACtC,qBACE,2BAAC,WAAK;wDACJ,SAAQ;wDACR,aAAY;wDACZ,MAAK;wDACL,QAAQ;;;;;;gDAGd;4CACF,CAAA;;;;;;sDAIF,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,KAAK;sDAC/C,AAAC,CAAA;gDACA,IAAI;oDACF,qBAAO,2BAAC,qBAAY;;;;;gDACtB,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,sBAAsB;oDACpC,qBACE,2BAAC,WAAK;wDACJ,SAAQ;wDACR,aAAY;wDACZ,MAAK;wDACL,QAAQ;;;;;;gDAGd;4CACF,CAAA;;;;;;;;;;;;;;;;;;;;;;sCAOR,2BAAC,oBAAe;;;;;;;YAGpB;eA9NM;;oBAC8B,aAAQ;;;iBADtC;gBAgON,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDrOD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}