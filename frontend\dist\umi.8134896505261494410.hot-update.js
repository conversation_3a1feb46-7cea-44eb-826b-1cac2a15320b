globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/pages/personal-center/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
            var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
            var _UserProfileCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserProfileCard.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const PersonalCenterPage = ()=>{
                _s();
                const { initialState, loading } = (0, _max.useModel)('@@initialState');
                // 如果正在加载，显示加载状态
                if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        minHeight: '100vh',
                        background: '#f5f8ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            size: "large"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 24,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginLeft: 16
                            },
                            children: "正在加载用户信息..."
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 25,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 15,
                    columnNumber: 7
                }, this);
                // 如果用户未登录，跳转到登录页
                (0, _react.useEffect)(()=>{
                    if (!loading && !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) _max.history.push('/user/login');
                }, [
                    loading,
                    initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
                ]);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                minHeight: '100vh',
                                background: '#f5f8ff',
                                padding: '12px 12px 24px 12px'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                style: {
                                    width: '100%',
                                    minHeight: 'calc(100vh - 48px)',
                                    borderRadius: '12px',
                                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                                },
                                styles: {
                                    body: {
                                        padding: '24px'
                                    }
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: [
                                        16,
                                        16
                                    ],
                                    style: {
                                        margin: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            style: {
                                                marginBottom: 8
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileCard.default, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 63,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 62,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            style: {
                                                marginBottom: 8
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 76,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 67,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 24,
                                            lg: 12,
                                            xl: 12,
                                            xxl: 12,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/index.tsx",
                                                lineNumber: 81,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/index.tsx",
                                            lineNumber: 80,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 60,
                                    columnNumber: 9
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 47,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 39,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                            fileName: "src/pages/personal-center/index.tsx",
                            lineNumber: 88,
                            columnNumber: 5
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(PersonalCenterPage, "G/2lp/LTMe9ZvmWNgegOr1tze1g=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = PersonalCenterPage;
            var _default = PersonalCenterPage;
            var _c;
            $RefreshReg$(_c, "PersonalCenterPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/components/FloatButton/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _SubscriptionPlansContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/subscription/components/SubscriptionPlansContent.tsx"));
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const UserFloatButton = ({ style })=>{
                _s();
                const [open, setOpen] = (0, _react.useState)(false);
                const [logoutModalVisible, setLogoutModalVisible] = (0, _react.useState)(false);
                const [logoutLoading, setLogoutLoading] = (0, _react.useState)(false);
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                const [activeTab, setActiveTab] = (0, _react.useState)('profile');
                const [profileForm] = _antd.Form.useForm();
                const [profileLoading, setProfileLoading] = (0, _react.useState)(false);
                const [currentSubscription, setCurrentSubscription] = (0, _react.useState)(null);
                const [subscriptionLoading, setSubscriptionLoading] = (0, _react.useState)(false);
                const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
                // 如果用户未登录，不显示浮动按钮
                // 确保用户已登录后就显示 FloatButton，无需等待团队选择
                if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) return null;
                // 处理个人中心跳转
                const handlePersonalCenter = ()=>{
                    setOpen(false);
                    _max.history.push('/personal-center');
                };
                // 处理设置
                const handleSettings = async ()=>{
                    setOpen(false);
                    if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) profileForm.setFieldsValue({
                        name: initialState.currentUser.name,
                        email: initialState.currentUser.email,
                        telephone: initialState.currentUser.telephone || ''
                    });
                    // 获取当前订阅信息
                    setSubscriptionLoading(true);
                    try {
                        const subscription = await _services.SubscriptionService.getCurrentSubscription();
                        setCurrentSubscription(subscription);
                    } catch (error) {
                        console.error('获取当前订阅失败:', error);
                        setCurrentSubscription(null);
                    } finally{
                        setSubscriptionLoading(false);
                    }
                    setSettingsModalVisible(true);
                };
                // 处理帮助文档
                const handleHelp = ()=>{
                    setOpen(false);
                    _max.history.push('/help');
                };
                // 保存用户资料
                const handleSaveProfile = async (values)=>{
                    try {
                        setProfileLoading(true);
                        const updateData = {
                            name: values.name,
                            telephone: values.telephone
                        };
                        const updatedProfile = await _services.UserService.updateUserProfile(updateData);
                        // 更新 initialState 中的用户信息
                        await setInitialState((prevState)=>({
                                ...prevState,
                                currentUser: {
                                    ...prevState === null || prevState === void 0 ? void 0 : prevState.currentUser,
                                    ...updatedProfile
                                }
                            }));
                        _antd.message.success('个人资料更新成功');
                    } catch (error) {
                        console.error('更新个人资料失败:', error);
                        _antd.message.error('更新个人资料失败');
                    } finally{
                        setProfileLoading(false);
                    }
                };
                // 处理订阅成功
                const handleSubscriptionSuccess = async ()=>{
                    // 刷新当前订阅信息
                    try {
                        const subscription = await _services.SubscriptionService.getCurrentSubscription();
                        setCurrentSubscription(subscription);
                    } catch (error) {
                        console.error('刷新订阅信息失败:', error);
                    }
                    setSettingsModalVisible(false);
                    _antd.message.success('订阅成功！');
                };
                // 计算剩余天数
                const calculateRemainingDays = (endDate)=>{
                    const end = new Date(endDate);
                    const now = new Date();
                    const diffTime = end.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / 86400000);
                    return Math.max(0, diffDays);
                };
                // 处理退出登录
                const handleLogout = async ()=>{
                    try {
                        setLogoutLoading(true);
                        // 调用退出登录API
                        await _services.AuthService.logout();
                        // 清除 initialState
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        // 跳转到登录页面
                        _max.history.push('/user/login');
                        _antd.message.success('已成功退出登录');
                    } catch (error) {
                        console.error('退出登录失败:', error);
                        // 即使API调用失败，也要清除本地状态并跳转
                        if (setInitialState) await setInitialState({
                            currentUser: undefined,
                            currentTeam: undefined
                        });
                        _max.history.push('/user/login');
                        _antd.message.warning('退出登录完成');
                    } finally{
                        setLogoutLoading(false);
                        setLogoutModalVisible(false);
                        setOpen(false);
                    }
                };
                const floatButtonItems = [
                    {
                        key: 'personal-center',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 193,
                            columnNumber: 13
                        }, this),
                        tooltip: '个人中心',
                        onClick: handlePersonalCenter
                    },
                    {
                        key: 'settings',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 199,
                            columnNumber: 13
                        }, this),
                        tooltip: '设置',
                        onClick: handleSettings
                    },
                    {
                        key: 'help',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.QuestionCircleOutlined, {}, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 205,
                            columnNumber: 13
                        }, this),
                        tooltip: '帮助文档',
                        onClick: handleHelp
                    },
                    {
                        key: 'logout',
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {}, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 211,
                            columnNumber: 13
                        }, this),
                        tooltip: '退出登录',
                        onClick: ()=>{
                            setOpen(false);
                            setLogoutModalVisible(true);
                        }
                    }
                ];
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.FloatButton.Group, {
                            open: open,
                            trigger: "click",
                            type: "primary",
                            placement: "left",
                            style: {
                                right: 24,
                                bottom: 24,
                                ...style
                            },
                            icon: open ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseOutlined, {}, void 0, false, {
                                fileName: "src/components/FloatButton/index.tsx",
                                lineNumber: 232,
                                columnNumber: 22
                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MenuOutlined, {}, void 0, false, {
                                fileName: "src/components/FloatButton/index.tsx",
                                lineNumber: 232,
                                columnNumber: 42
                            }, void 0),
                            onClick: ()=>setOpen(!open),
                            children: floatButtonItems.map((item)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.FloatButton, {
                                    icon: item.icon,
                                    tooltip: item.tooltip,
                                    onClick: item.onClick
                                }, item.key, false, {
                                    fileName: "src/components/FloatButton/index.tsx",
                                    lineNumber: 236,
                                    columnNumber: 11
                                }, this))
                        }, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 222,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                        fileName: "src/components/FloatButton/index.tsx",
                                        lineNumber: 249,
                                        columnNumber: 13
                                    }, void 0),
                                    "设置"
                                ]
                            }, void 0, true, {
                                fileName: "src/components/FloatButton/index.tsx",
                                lineNumber: 248,
                                columnNumber: 11
                            }, void 0),
                            open: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            footer: null,
                            width: 1200,
                            style: {
                                top: 20
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                                activeKey: activeTab,
                                onChange: setActiveTab,
                                items: [
                                    {
                                        key: 'profile',
                                        label: '个人资料',
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '20px 0'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                                form: profileForm,
                                                layout: "vertical",
                                                onFinish: handleSaveProfile,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                        label: "用户名",
                                                        name: "name",
                                                        rules: [
                                                            {
                                                                required: true,
                                                                message: '请输入用户名'
                                                            },
                                                            {
                                                                max: 100,
                                                                message: '用户名不能超过100个字符'
                                                            }
                                                        ],
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                lineNumber: 283,
                                                                columnNumber: 33
                                                            }, void 0),
                                                            placeholder: "请输入用户名"
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 282,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/components/FloatButton/index.tsx",
                                                        lineNumber: 274,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                        label: "邮箱地址",
                                                        name: "email",
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                lineNumber: 290,
                                                                columnNumber: 33
                                                            }, void 0),
                                                            disabled: true,
                                                            placeholder: "邮箱地址不可修改"
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 289,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/components/FloatButton/index.tsx",
                                                        lineNumber: 288,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                        label: "手机号",
                                                        name: "telephone",
                                                        rules: [
                                                            {
                                                                pattern: /^1[3-9]\d{9}$/,
                                                                message: '请输入正确的手机号格式'
                                                            }
                                                        ],
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {}, void 0, false, {
                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                lineNumber: 307,
                                                                columnNumber: 33
                                                            }, void 0),
                                                            placeholder: "请输入手机号",
                                                            maxLength: 11
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 306,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/components/FloatButton/index.tsx",
                                                        lineNumber: 296,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                        style: {
                                                            marginBottom: 0,
                                                            textAlign: 'right'
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    onClick: ()=>setSettingsModalVisible(false),
                                                                    children: "取消"
                                                                }, void 0, false, {
                                                                    fileName: "src/components/FloatButton/index.tsx",
                                                                    lineNumber: 315,
                                                                    columnNumber: 25
                                                                }, void 0),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    type: "primary",
                                                                    htmlType: "submit",
                                                                    loading: profileLoading,
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                        lineNumber: 322,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    children: "保存修改"
                                                                }, void 0, false, {
                                                                    fileName: "src/components/FloatButton/index.tsx",
                                                                    lineNumber: 318,
                                                                    columnNumber: 25
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 314,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/components/FloatButton/index.tsx",
                                                        lineNumber: 313,
                                                        columnNumber: 21
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/components/FloatButton/index.tsx",
                                                lineNumber: 269,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/components/FloatButton/index.tsx",
                                            lineNumber: 267,
                                            columnNumber: 17
                                        }, void 0)
                                    },
                                    {
                                        key: 'subscription',
                                        label: '订阅套餐',
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                padding: '20px 0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginBottom: 24
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                            level: 4,
                                                            style: {
                                                                marginBottom: 16
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                    style: {
                                                                        marginRight: 8,
                                                                        color: '#faad14'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/components/FloatButton/index.tsx",
                                                                    lineNumber: 340,
                                                                    columnNumber: 23
                                                                }, void 0),
                                                                "当前套餐"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 339,
                                                            columnNumber: 21
                                                        }, void 0),
                                                        subscriptionLoading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center',
                                                                padding: '20px 0'
                                                            },
                                                            children: "加载中..."
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 347,
                                                            columnNumber: 23
                                                        }, void 0) : currentSubscription ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            style: {
                                                                background: '#f6ffed',
                                                                border: '1px solid #b7eb8f',
                                                                marginBottom: 16
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                direction: "vertical",
                                                                style: {
                                                                    width: '100%'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            display: 'flex',
                                                                            justifyContent: 'space-between',
                                                                            alignItems: 'center'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                                strong: true,
                                                                                style: {
                                                                                    fontSize: 16
                                                                                },
                                                                                children: currentSubscription.planName
                                                                            }, void 0, false, {
                                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                                lineNumber: 367,
                                                                                columnNumber: 29
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                                                color: currentSubscription.status === 'ACTIVE' ? 'green' : 'orange',
                                                                                children: currentSubscription.status === 'ACTIVE' ? '有效' : '已过期'
                                                                            }, void 0, false, {
                                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                                lineNumber: 370,
                                                                                columnNumber: 29
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                        lineNumber: 360,
                                                                        columnNumber: 27
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                        type: "secondary",
                                                                        children: currentSubscription.planDescription
                                                                    }, void 0, false, {
                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                        lineNumber: 383,
                                                                        columnNumber: 27
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        style: {
                                                                            display: 'flex',
                                                                            justifyContent: 'space-between'
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                                        lineNumber: 394,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                                        children: [
                                                                                            "到期时间:",
                                                                                            ' ',
                                                                                            new Date(currentSubscription.endDate).toLocaleDateString()
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                                        lineNumber: 395,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                                lineNumber: 393,
                                                                                columnNumber: 29
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                                        lineNumber: 403,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                                        children: [
                                                                                            "剩余:",
                                                                                            ' ',
                                                                                            calculateRemainingDays(currentSubscription.endDate),
                                                                                            ' ',
                                                                                            "天"
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                                        lineNumber: 404,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                                lineNumber: 402,
                                                                                columnNumber: 29
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/components/FloatButton/index.tsx",
                                                                        lineNumber: 387,
                                                                        columnNumber: 27
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                lineNumber: 359,
                                                                columnNumber: 25
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 351,
                                                            columnNumber: 23
                                                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            style: {
                                                                background: '#fff2e8',
                                                                border: '1px solid #ffbb96',
                                                                marginBottom: 16
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                type: "secondary",
                                                                children: "您当前没有有效的订阅套餐，请选择合适的套餐进行订阅"
                                                            }, void 0, false, {
                                                                fileName: "src/components/FloatButton/index.tsx",
                                                                lineNumber: 424,
                                                                columnNumber: 25
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 416,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/components/FloatButton/index.tsx",
                                                    lineNumber: 338,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                            level: 4,
                                                            style: {
                                                                marginBottom: 16
                                                            },
                                                            children: "选择套餐"
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 433,
                                                            columnNumber: 21
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_SubscriptionPlansContent.default, {
                                                            onSubscriptionSuccess: handleSubscriptionSuccess
                                                        }, void 0, false, {
                                                            fileName: "src/components/FloatButton/index.tsx",
                                                            lineNumber: 436,
                                                            columnNumber: 21
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/components/FloatButton/index.tsx",
                                                    lineNumber: 432,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/components/FloatButton/index.tsx",
                                            lineNumber: 336,
                                            columnNumber: 17
                                        }, void 0)
                                    }
                                ]
                            }, void 0, false, {
                                fileName: "src/components/FloatButton/index.tsx",
                                lineNumber: 259,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 246,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "确认退出登录",
                            open: logoutModalVisible,
                            onCancel: ()=>setLogoutModalVisible(false),
                            onOk: handleLogout,
                            confirmLoading: logoutLoading,
                            okText: "确认退出",
                            cancelText: "取消",
                            okButtonProps: {
                                danger: true
                            },
                            width: 400,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    padding: '20px 0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                                        style: {
                                            fontSize: 48,
                                            color: '#ff4d4f',
                                            marginBottom: 16
                                        }
                                    }, void 0, false, {
                                        fileName: "src/components/FloatButton/index.tsx",
                                        lineNumber: 460,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            fontSize: 16,
                                            fontWeight: 'bold',
                                            marginBottom: 8
                                        },
                                        children: "您确定要退出登录吗？"
                                    }, void 0, false, {
                                        fileName: "src/components/FloatButton/index.tsx",
                                        lineNumber: 463,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            color: '#666',
                                            fontSize: 14
                                        },
                                        children: "退出后您需要重新登录才能继续使用系统"
                                    }, void 0, false, {
                                        fileName: "src/components/FloatButton/index.tsx",
                                        lineNumber: 466,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/components/FloatButton/index.tsx",
                                lineNumber: 459,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/components/FloatButton/index.tsx",
                            lineNumber: 448,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(UserFloatButton, "crDVL7l+hl1o/b8mWKYgoEHO6cA=", false, function() {
                return [
                    _antd.Form.useForm,
                    _max.useModel
                ];
            });
            _c = UserFloatButton;
            var _default = UserFloatButton;
            var _c;
            $RefreshReg$(_c, "UserFloatButton");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '14447076999828479884';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.8134896505261494410.hot-update.js.map