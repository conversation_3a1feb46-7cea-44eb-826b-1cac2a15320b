{"version": 3, "sources": ["umi.4627203728002574714.hot-update.js", "src/services/subscription.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='750508250767452519';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 订阅管理相关 API 服务\n */\n\nimport type {\n  CreateSubscriptionRequest,\n  SubscriptionPlanResponse,\n  SubscriptionResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 订阅服务类\n */\nexport class SubscriptionService {\n  /**\n   * 获取所有订阅套餐（公开接口）\n   */\n  static async getAllPlans(): Promise<SubscriptionPlanResponse[]> {\n    const response = await apiRequest.get<SubscriptionPlanResponse[]>('/plans');\n    return response.data;\n  }\n\n  /**\n   * 获取活跃的订阅套餐\n   */\n  static async getActivePlans(): Promise<SubscriptionPlanResponse[]> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    return allPlans.filter((plan) => plan.isActive);\n  }\n\n  /**\n   * 根据 ID 获取订阅套餐详情\n   */\n  static async getPlanById(planId: number): Promise<SubscriptionPlanResponse> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    const plan = allPlans.find((p) => p.id === planId);\n\n    if (!plan) {\n      throw new Error('订阅套餐不存在');\n    }\n\n    return plan;\n  }\n\n  /**\n   * 获取当前用户的有效订阅\n   */\n  static async getCurrentSubscription(): Promise<SubscriptionResponse | null> {\n    try {\n      const response = await apiRequest.get<SubscriptionResponse>(\n        '/subscriptions/current',\n      );\n      return response.data;\n    } catch (error: any) {\n      // 如果没有订阅，返回 null\n      if (error?.response?.status === 404) {\n        return null;\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 创建订阅\n   */\n  static async createSubscription(\n    data: CreateSubscriptionRequest,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      '/subscriptions',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取用户订阅列表\n   */\n  static async getUserSubscriptions(): Promise<SubscriptionResponse[]> {\n    const response =\n      await apiRequest.get<SubscriptionResponse[]>('/subscriptions');\n    return response.data;\n  }\n\n  /**\n   * 取消订阅\n   */\n  static async cancelSubscription(subscriptionId: number): Promise<void> {\n    const response = await apiRequest.post<void>(\n      `/subscriptions/${subscriptionId}/cancel`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 续费订阅\n   */\n  static async renewSubscription(\n    subscriptionId: number,\n    duration: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/renew`,\n      { duration },\n    );\n    return response.data;\n  }\n\n  /**\n   * 升级订阅套餐\n   */\n  static async upgradeSubscription(\n    subscriptionId: number,\n    newPlanId: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/upgrade`,\n      { planId: newPlanId },\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取订阅使用统计\n   */\n  static async getSubscriptionUsage(): Promise<{\n    currentUsage: number;\n    maxUsage: number;\n    usagePercentage: number;\n    remainingDays: number;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        currentUsage: 0,\n        maxUsage: 0,\n        usagePercentage: 0,\n        remainingDays: 0,\n      };\n    }\n\n    // 计算剩余天数\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const remainingDays = Math.max(\n      0,\n      Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)),\n    );\n\n    // 这里需要后端提供实际的使用量数据\n    // 暂时返回模拟数据\n    const currentUsage = 0; // 实际使用量\n    const maxUsage = currentSubscription.maxSize;\n    const usagePercentage = maxUsage > 0 ? (currentUsage / maxUsage) * 100 : 0;\n\n    return {\n      currentUsage,\n      maxUsage,\n      usagePercentage,\n      remainingDays,\n    };\n  }\n\n  /**\n   * 获取订阅历史记录\n   */\n  static async getSubscriptionHistory(): Promise<SubscriptionResponse[]> {\n    const subscriptions = await SubscriptionService.getUserSubscriptions();\n\n    // 按创建时间倒序排列\n    return subscriptions.sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),\n    );\n  }\n\n  /**\n   * 计算套餐价格（考虑折扣等）\n   */\n  static calculatePlanPrice(\n    plan: SubscriptionPlanResponse,\n    duration: number,\n  ): {\n    originalPrice: number;\n    discountedPrice: number;\n    discount: number;\n    totalPrice: number;\n  } {\n    const originalPrice = plan.price * duration;\n    let discount = 0;\n\n    // 根据订阅时长给予折扣\n    if (duration >= 12) {\n      discount = 0.2; // 年付8折\n    } else if (duration >= 6) {\n      discount = 0.1; // 半年付9折\n    }\n\n    const discountedPrice = originalPrice * (1 - discount);\n\n    return {\n      originalPrice,\n      discountedPrice,\n      discount: discount * 100,\n      totalPrice: discountedPrice,\n    };\n  }\n\n  /**\n   * 比较套餐功能\n   */\n  static comparePlans(plans: SubscriptionPlanResponse[]): Array<{\n    feature: string;\n    values: Array<string | number | boolean>;\n  }> {\n    return [\n      {\n        feature: '数据存储上限',\n        values: plans.map((plan) => plan.maxSize),\n      },\n      {\n        feature: '月费价格',\n        values: plans.map((plan) => `¥${plan.price}`),\n      },\n      {\n        feature: '技术支持',\n        values: plans.map((plan) => (plan.price > 0 ? '7x24小时' : '工作日')),\n      },\n      {\n        feature: '数据备份',\n        values: plans.map((plan) => plan.price > 0),\n      },\n      {\n        feature: '高级功能',\n        values: plans.map((plan) => plan.price >= 100),\n      },\n    ];\n  }\n\n  /**\n   * 检查订阅状态\n   */\n  static async checkSubscriptionStatus(): Promise<{\n    hasActiveSubscription: boolean;\n    isExpiringSoon: boolean;\n    daysUntilExpiry: number;\n    needsUpgrade: boolean;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        hasActiveSubscription: false,\n        isExpiringSoon: false,\n        daysUntilExpiry: 0,\n        needsUpgrade: false,\n      };\n    }\n\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const daysUntilExpiry = Math.ceil(\n      (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),\n    );\n    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n\n    // 检查是否需要升级（基于使用量）\n    const usage = await SubscriptionService.getSubscriptionUsage();\n    const needsUpgrade = usage.usagePercentage > 80;\n\n    return {\n      hasActiveSubscription: true,\n      isExpiringSoon,\n      daysUntilExpiry,\n      needsUpgrade,\n    };\n  }\n}\n\n// 导出默认实例\nexport default SubscriptionService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCaA,mBAAmB;2BAAnB;;gBA6Qb,SAAS;gBACT,OAAmC;2BAAnC;;;;;4CArR2B;;;;;;;;;YAOpB,MAAM;gBACX;;GAEC,GACD,aAAa,cAAmD;oBAC9D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA6B;oBAClE,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,iBAAsD;oBACjE,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,OAAO,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;gBAChD;gBAEA;;GAEC,GACD,aAAa,YAAY,MAAc,EAAqC;oBAC1E,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,MAAM,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAE3C,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;oBAGlB,OAAO;gBACT;gBAEA;;GAEC,GACD,aAAa,yBAA+D;oBAC1E,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;wBAEF,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAO,OAAY;4BAEf;wBADJ,iBAAiB;wBACjB,IAAI,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,MAAK,KAC9B,OAAO;wBAET,MAAM;oBACR;gBACF;gBAEA;;GAEC,GACD,aAAa,mBACX,IAA+B,EACA;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAAwD;oBACnE,MAAM,WACJ,MAAM,mBAAU,CAAC,GAAG,CAAyB;oBAC/C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,mBAAmB,cAAsB,EAAiB;oBACrE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,OAAO,CAAC;oBAE3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBACX,cAAsB,EACtB,QAAgB,EACe;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,EACxC;wBAAE;oBAAS;oBAEb,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,oBACX,cAAsB,EACtB,SAAiB,EACc;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,QAAQ,CAAC,EAC1C;wBAAE,QAAQ;oBAAU;oBAEtB,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,cAAc;wBACd,UAAU;wBACV,iBAAiB;wBACjB,eAAe;oBACjB;oBAGF,SAAS;oBACT,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,gBAAgB,KAAK,GAAG,CAC5B,GACA,KAAK,IAAI,CAAC,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAGnD,mBAAmB;oBACnB,WAAW;oBACX,MAAM,eAAe,GAAG,QAAQ;oBAChC,MAAM,WAAW,oBAAoB,OAAO;oBAC5C,MAAM,kBAAkB,WAAW,IAAI,AAAC,eAAe,WAAY,MAAM;oBAEzE,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF;gBAEA;;GAEC,GACD,aAAa,yBAA0D;oBACrE,MAAM,gBAAgB,MAAM,oBAAoB,oBAAoB;oBAEpE,YAAY;oBACZ,OAAO,cAAc,IAAI,CACvB,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAErE;gBAEA;;GAEC,GACD,OAAO,mBACL,IAA8B,EAC9B,QAAgB,EAMhB;oBACA,MAAM,gBAAgB,KAAK,KAAK,GAAG;oBACnC,IAAI,WAAW;oBAEf,aAAa;oBACb,IAAI,YAAY,IACd,WAAW,KAAK,OAAO;yBAClB,IAAI,YAAY,GACrB,WAAW,KAAK,QAAQ;oBAG1B,MAAM,kBAAkB,gBAAiB,CAAA,IAAI,QAAO;oBAEpD,OAAO;wBACL;wBACA;wBACA,UAAU,WAAW;wBACrB,YAAY;oBACd;gBACF;gBAEA;;GAEC,GACD,OAAO,aAAa,KAAiC,EAGlD;oBACD,OAAO;wBACL;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;wBAC1C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;wBAC9C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAU,KAAK,KAAK,GAAG,IAAI,WAAW;wBAC3D;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,GAAG;wBAC3C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,IAAI;wBAC5C;qBACD;gBACH;gBAEA;;GAEC,GACD,aAAa,0BAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,uBAAuB;wBACvB,gBAAgB;wBAChB,iBAAiB;wBACjB,cAAc;oBAChB;oBAGF,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,kBAAkB,KAAK,IAAI,CAC/B,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAEzC,MAAM,iBAAiB,mBAAmB,KAAK,kBAAkB;oBAEjE,kBAAkB;oBAClB,MAAM,QAAQ,MAAM,oBAAoB,oBAAoB;oBAC5D,MAAM,eAAe,MAAM,eAAe,GAAG;oBAE7C,OAAO;wBACL,uBAAuB;wBACvB;wBACA;wBACA;oBACF;gBACF;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;ID3RD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}