{"version": 3, "sources": ["umi.3551152474990421111.hot-update.js", "src/services/user.ts", "src/utils/errorHandler.ts", "src/constants/responseCodes.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='13942673161251361313';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 用户管理相关 API 服务\n */\n\nimport type {\n  UpdateUserProfileRequest,\n  UserPersonalStatsResponse,\n  UserProfileDetailResponse,\n  UserProfileResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 用户服务类\n *\n * 提供用户相关的所有API接口，包括：\n * - 用户资料管理（查看、更新）\n * - 密码修改\n * - 用户统计信息\n * - 账户管理\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class UserService {\n  /**\n   * 获取当前用户资料\n   *\n   * 获取当前登录用户的基本资料信息，包括姓名、邮箱、电话等。\n   * 需要有效的用户Token。\n   *\n   * @returns Promise<UserProfileResponse> 用户资料信息\n   * @throws 当用户未登录或Token无效时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const profile = await UserService.getUserProfile();\n   * console.log('用户姓名:', profile.name);\n   * console.log('用户邮箱:', profile.email);\n   * ```\n   */\n  static async getUserProfile(): Promise<UserProfileResponse> {\n    const response =\n      await apiRequest.get<UserProfileResponse>('/users/profile');\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 更新用户资料\n   *\n   * 更新当前用户的资料信息。可以更新姓名、电话、职位等信息。\n   * 邮箱通常不允许修改，因为它是用户的唯一标识。\n   *\n   * @param data 用户资料更新请求参数\n   * @param data.name 用户姓名\n   * @param data.telephone 电话号码（可选）\n   * @param data.position 职位（可选）\n   * @returns Promise<UserProfileResponse> 更新后的用户资料\n   * @throws 当数据验证失败或用户无权限时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const updatedProfile = await UserService.updateUserProfile({\n   *   name: '张三',\n   *   telephone: '13800138000',\n   *   position: '项目经理'\n   * });\n   * console.log('资料更新成功:', updatedProfile);\n   * ```\n   */\n  static async updateUserProfile(\n    data: UpdateUserProfileRequest,\n  ): Promise<UserProfileResponse> {\n    const response = await apiRequest.put<UserProfileResponse>(\n      '/users/profile',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 修改密码\n   *\n   * 修改当前用户的登录密码。需要提供当前密码进行验证。\n   *\n   * @param currentPassword 当前密码\n   * @param newPassword 新密码（8-20位，包含字母和数字）\n   * @returns Promise<void> 修改成功时resolve\n   * @throws 当当前密码错误或新密码格式不正确时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await UserService.changePassword('oldPassword123', 'newPassword456');\n   * console.log('密码修改成功');\n   * ```\n   */\n  static async changePassword(\n    currentPassword: string,\n    newPassword: string,\n  ): Promise<void> {\n    const data: UpdateUserProfileRequest = {\n      currentPassword,\n      newPassword,\n    };\n\n    const response = await apiRequest.put<void>('/users/profile', data);\n    return response.data;\n  }\n\n  /**\n   * 更新用户名\n   */\n  static async updateUserName(name: string): Promise<UserProfileResponse> {\n    const data: UpdateUserProfileRequest = {\n      name,\n    };\n\n    const response = await apiRequest.put<UserProfileResponse>(\n      '/users/profile',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 验证当前密码\n   */\n  static async validateCurrentPassword(password: string): Promise<boolean> {\n    try {\n      const response = await apiRequest.post<boolean>(\n        '/users/validate-password',\n        { password },\n      );\n      return response.data;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取用户统计信息\n   *\n   * 获取用户的团队统计信息，包括总团队数、创建的团队数等。\n   * 注意：当前返回模拟数据，等待后端实现专门的统计接口。\n   *\n   * @returns Promise<object> 用户统计信息\n   * @returns Promise<object>.totalTeams 总团队数\n   * @returns Promise<object>.createdTeams 创建的团队数\n   * @returns Promise<object>.joinedTeams 加入的团队数\n   * @returns Promise<object>.lastLoginTime 最后登录时间\n   *\n   * @example\n   * ```typescript\n   * const stats = await UserService.getUserStats();\n   * console.log('总团队数:', stats.totalTeams);\n   * console.log('创建的团队:', stats.createdTeams);\n   * ```\n   */\n  static async getUserStats(): Promise<{\n    totalTeams: number;\n    createdTeams: number;\n    joinedTeams: number;\n    lastLoginTime: string;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时返回模拟数据\n    return {\n      totalTeams: 0,\n      createdTeams: 0,\n      joinedTeams: 0,\n      lastLoginTime: new Date().toISOString(),\n    };\n  }\n\n  /**\n   * 获取用户个人统计数据\n   *\n   * 获取用户在当前团队中的业务统计数据，包括车辆、人员、预警、告警等数量。\n   * 这些数据用于个人中心的统计卡片显示。\n   *\n   * @returns Promise<UserPersonalStatsResponse> 个人统计数据\n   * @throws 当用户未选择团队或无权限时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const personalStats = await UserService.getUserPersonalStats();\n   * console.log('车辆数量:', personalStats.vehicles);\n   * console.log('人员数量:', personalStats.personnel);\n   * console.log('预警数量:', personalStats.warnings);\n   * console.log('告警数量:', personalStats.alerts);\n   * ```\n   */\n  static async getUserPersonalStats(): Promise<UserPersonalStatsResponse> {\n    const response = await apiRequest.get<UserPersonalStatsResponse>(\n      '/users/personal-stats',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取用户详细信息\n   *\n   * 获取用户的详细资料信息，包括基本信息、注册时间、最后登录信息等。\n   * 比getUserProfile返回更多的详细信息，用于个人中心展示。\n   *\n   * @returns Promise<UserProfileDetailResponse> 用户详细信息\n   * @throws 当用户未登录或Token无效时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const detail = await UserService.getUserProfileDetail();\n   * console.log('注册时间:', detail.registerDate);\n   * console.log('最后登录:', detail.lastLoginTime);\n   * console.log('团队数量:', detail.teamCount);\n   * ```\n   */\n  static async getUserProfileDetail(): Promise<UserProfileDetailResponse> {\n    const response = await apiRequest.get<UserProfileDetailResponse>(\n      '/users/profile-detail',\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 删除用户账户\n   */\n  static async deleteAccount(password: string): Promise<void> {\n    const response = await apiRequest.delete<void>('/users/account', {\n      password,\n    });\n    return response.data;\n  }\n}\n\n// 导出默认实例\nexport default UserService;\n", "/**\n * 集中式错误消息处理工具\n * \n * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。\n * 支持message和notification组件，确保错误消息显示的一致性。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { ResponseCode, isError, getDescription } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\nimport { AuthService } from '@/services';\n\n// ============= 错误显示类型 =============\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 'silent',\n  /** 使用message.warning显示 */\n  WARNING = 'warning',\n  /** 使用message.error显示 */\n  ERROR = 'error',\n  /** 使用notification显示 */\n  NOTIFICATION = 'notification',\n  /** 重定向到登录页 */\n  REDIRECT = 'redirect',\n}\n\n// ============= 错误处理配置 =============\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否显示详细错误信息 */\n  showDetails?: boolean;\n  /** 错误回调函数 */\n  onError?: (error: any) => void;\n}\n\n/**\n * 默认错误处理配置\n */\nconst DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {\n  [ResponseCode.BAD_REQUEST]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.UNAUTHORIZED]: {\n    displayType: ErrorDisplayType.REDIRECT,\n  },\n  [ResponseCode.FORBIDDEN]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.NOT_FOUND]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.CONFLICT]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.UNPROCESSABLE_ENTITY]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.TOO_MANY_REQUESTS]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.INTERNAL_SERVER_ERROR]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务器内部错误，请稍后重试',\n  },\n  [ResponseCode.BAD_GATEWAY]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '网关错误，请稍后重试',\n  },\n  [ResponseCode.SERVICE_UNAVAILABLE]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务暂时不可用，请稍后重试',\n  },\n  [ResponseCode.GATEWAY_TIMEOUT]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '请求超时，请稍后重试',\n  },\n};\n\n// ============= 核心错误处理方法 =============\n\n/**\n * 处理API响应错误\n * \n * @param response API响应对象\n * @param config 错误处理配置\n */\nexport const handleApiError = (\n  response: ApiResponse<any>,\n  config?: ErrorHandlerConfig\n): void => {\n  if (!response || ResponseCode.isSuccess(response.code)) {\n    return;\n  }\n\n  const errorCode = response.code;\n  const errorMessage = response.message || getDescription(errorCode);\n\n  // 调试日志\n  console.log('handleApiError 被调用:', {\n    errorCode,\n    errorMessage,\n    response,\n    config\n  });\n\n  // 合并配置\n  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {\n    displayType: ErrorDisplayType.ERROR,\n  };\n  const finalConfig = { ...defaultConfig, ...config };\n\n  // 使用自定义消息或响应消息\n  const displayMessage = finalConfig.customMessage || errorMessage;\n\n  console.log('错误处理配置:', {\n    defaultConfig,\n    finalConfig,\n    displayMessage\n  });\n\n  // 执行错误回调\n  if (finalConfig.onError) {\n    finalConfig.onError({ code: errorCode, message: errorMessage, response });\n  }\n\n  // 根据显示类型处理错误\n  switch (finalConfig.displayType) {\n    case ErrorDisplayType.SILENT:\n      console.log('静默处理错误');\n      break;\n\n    case ErrorDisplayType.WARNING:\n      console.log('显示警告消息:', displayMessage);\n      message.warning(displayMessage);\n      break;\n\n    case ErrorDisplayType.ERROR:\n      console.log('显示错误消息:', displayMessage);\n      message.error(displayMessage);\n      break;\n\n    case ErrorDisplayType.NOTIFICATION:\n      console.log('显示通知:', displayMessage);\n      notification.error({\n        message: '操作失败',\n        description: displayMessage,\n        duration: 4.5,\n      });\n      break;\n\n    case ErrorDisplayType.REDIRECT:\n      console.log('处理认证错误:', errorCode, displayMessage);\n      handleAuthError(errorCode, displayMessage);\n      break;\n\n    default:\n      console.log('默认错误处理:', displayMessage);\n      message.error(displayMessage);\n      break;\n  }\n};\n\n/**\n * 处理认证相关错误\n * \n * @param errorCode 错误代码\n * @param errorMessage 错误消息\n */\nconst handleAuthError = (errorCode: number, errorMessage: string): void => {\n  console.log('handleAuthError 被调用:', { errorCode, errorMessage });\n\n  if (errorCode === ResponseCode.UNAUTHORIZED) {\n    // 检查当前路径，避免在某些页面立即跳转\n    const currentPath = window.location.pathname;\n    const isDashboardRelated =\n      currentPath.startsWith('/dashboard') ||\n      currentPath.startsWith('/team');\n\n    console.log('401错误处理:', { currentPath, isDashboardRelated });\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      return;\n    }\n\n    // 清除认证信息并跳转到登录页\n    AuthService.clearTokens();\n    console.log('显示401错误消息并跳转登录页:', errorMessage);\n    message.error(errorMessage || '登录已过期，请重新登录');\n    history.push('/user/login');\n  } else if (errorCode === ResponseCode.FORBIDDEN) {\n    console.log('显示403错误消息:', errorMessage);\n    message.error(errorMessage || '没有权限访问该资源');\n  }\n};\n\n// ============= 便捷方法 =============\n\n/**\n * 显示成功消息\n * \n * @param message 成功消息\n */\nexport const showSuccess = (msg: string): void => {\n  message.success(msg);\n};\n\n/**\n * 显示警告消息\n * \n * @param message 警告消息\n */\nexport const showWarning = (msg: string): void => {\n  message.warning(msg);\n};\n\n/**\n * 显示错误消息\n * \n * @param message 错误消息\n */\nexport const showError = (msg: string): void => {\n  message.error(msg);\n};\n\n/**\n * 显示信息消息\n * \n * @param message 信息消息\n */\nexport const showInfo = (msg: string): void => {\n  message.info(msg);\n};\n\n/**\n * 显示通知\n * \n * @param title 通知标题\n * @param description 通知描述\n * @param type 通知类型\n */\nexport const showNotification = (\n  title: string,\n  description: string,\n  type: 'success' | 'info' | 'warning' | 'error' = 'info'\n): void => {\n  notification[type]({\n    message: title,\n    description,\n    duration: 4.5,\n  });\n};\n\n// ============= 错误处理Hook =============\n\n/**\n * 创建错误处理器\n * \n * @param config 默认错误处理配置\n * @returns 错误处理函数\n */\nexport const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {\n  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {\n    const finalConfig = { ...defaultConfig, ...config };\n    handleApiError(response, finalConfig);\n  };\n};\n\n// ============= 默认导出 =============\n\nexport default {\n  handleApiError,\n  showSuccess,\n  showWarning,\n  showError,\n  showInfo,\n  showNotification,\n  createErrorHandler,\n  ErrorDisplayType,\n};\n", "/**\n * 标准化响应代码常量\n * \n * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。\n * 所有API响应处理都应该使用这些预定义的响应代码。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\n// ============= 成功状态码 =============\n\n/**\n * 操作成功\n */\nexport const SUCCESS = 200;\n\n// ============= 客户端错误状态码 =============\n\n/**\n * 请求参数错误或业务逻辑错误\n */\nexport const BAD_REQUEST = 400;\n\n/**\n * 未认证，需要登录\n */\nexport const UNAUTHORIZED = 401;\n\n/**\n * 权限不足，已认证但无权限访问\n */\nexport const FORBIDDEN = 403;\n\n/**\n * 资源不存在\n */\nexport const NOT_FOUND = 404;\n\n/**\n * 资源冲突（如重复创建、数据冲突等）\n */\nexport const CONFLICT = 409;\n\n/**\n * 请求格式正确但语义错误（如验证失败）\n */\nexport const UNPROCESSABLE_ENTITY = 422;\n\n/**\n * 请求频率限制\n */\nexport const TOO_MANY_REQUESTS = 429;\n\n// ============= 服务器错误状态码 =============\n\n/**\n * 服务器内部错误\n */\nexport const INTERNAL_SERVER_ERROR = 500;\n\n/**\n * 网关错误\n */\nexport const BAD_GATEWAY = 502;\n\n/**\n * 服务不可用\n */\nexport const SERVICE_UNAVAILABLE = 503;\n\n/**\n * 网关超时\n */\nexport const GATEWAY_TIMEOUT = 504;\n\n// ============= 响应代码分类方法 =============\n\n/**\n * 判断是否为成功响应代码\n * \n * @param code 响应代码\n * @returns 是否为成功代码\n */\nexport const isSuccess = (code: number): boolean => {\n  return code === SUCCESS;\n};\n\n/**\n * 判断是否为客户端错误代码\n * \n * @param code 响应代码\n * @returns 是否为客户端错误代码\n */\nexport const isClientError = (code: number): boolean => {\n  return code >= 400 && code < 500;\n};\n\n/**\n * 判断是否为服务器错误代码\n * \n * @param code 响应代码\n * @returns 是否为服务器错误代码\n */\nexport const isServerError = (code: number): boolean => {\n  return code >= 500 && code < 600;\n};\n\n/**\n * 判断是否为错误代码（非成功代码）\n * \n * @param code 响应代码\n * @returns 是否为错误代码\n */\nexport const isError = (code: number): boolean => {\n  return !isSuccess(code);\n};\n\n// ============= 响应代码描述方法 =============\n\n/**\n * 获取响应代码的默认描述\n * \n * @param code 响应代码\n * @returns 响应代码描述\n */\nexport const getDescription = (code: number): string => {\n  switch (code) {\n    case SUCCESS:\n      return '操作成功';\n    case BAD_REQUEST:\n      return '请求参数错误';\n    case UNAUTHORIZED:\n      return '未认证，需要登录';\n    case FORBIDDEN:\n      return '权限不足';\n    case NOT_FOUND:\n      return '资源不存在';\n    case CONFLICT:\n      return '资源冲突';\n    case UNPROCESSABLE_ENTITY:\n      return '请求语义错误';\n    case TOO_MANY_REQUESTS:\n      return '请求频率过高';\n    case INTERNAL_SERVER_ERROR:\n      return '服务器内部错误';\n    case BAD_GATEWAY:\n      return '网关错误';\n    case SERVICE_UNAVAILABLE:\n      return '服务不可用';\n    case GATEWAY_TIMEOUT:\n      return '网关超时';\n    default:\n      return '未知错误';\n  }\n};\n\n// ============= 响应代码集合 =============\n\n/**\n * 所有成功响应代码\n */\nexport const SUCCESS_CODES = [SUCCESS];\n\n/**\n * 所有客户端错误响应代码\n */\nexport const CLIENT_ERROR_CODES = [\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n];\n\n/**\n * 所有服务器错误响应代码\n */\nexport const SERVER_ERROR_CODES = [\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n];\n\n/**\n * 所有错误响应代码\n */\nexport const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];\n\n/**\n * 所有响应代码\n */\nexport const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];\n\n// ============= 默认导出 =============\n\n/**\n * 响应代码常量对象\n */\nexport const ResponseCode = {\n  // 成功状态码\n  SUCCESS,\n  \n  // 客户端错误状态码\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n  \n  // 服务器错误状态码\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n  \n  // 工具方法\n  isSuccess,\n  isClientError,\n  isServerError,\n  isError,\n  getDescription,\n  \n  // 代码集合\n  SUCCESS_CODES,\n  CLIENT_ERROR_CODES,\n  SERVER_ERROR_CODES,\n  ERROR_CODES,\n  ALL_CODES,\n} as const;\n\nexport default ResponseCode;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCuBA,WAAW;2BAAX;;gBA2Nb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CA5O2B;kDACE;iDACE;;;;;;;;;YAcxB,MAAM;gBACX;;;;;;;;;;;;;;;GAeC,GACD,aAAa,iBAA+C;oBAC1D,MAAM,WACJ,MAAM,mBAAU,CAAC,GAAG,CAAsB;oBAE5C,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,kBACX,IAA8B,EACA;oBAC9B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;GAeC,GACD,aAAa,eACX,eAAuB,EACvB,WAAmB,EACJ;oBACf,MAAM,OAAiC;wBACrC;wBACA;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAO,kBAAkB;oBAC9D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAAgC;oBACtE,MAAM,OAAiC;wBACrC;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,wBAAwB,QAAgB,EAAoB;oBACvE,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,4BACA;4BAAE;wBAAS;wBAEb,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,aAAa,eAKV;oBACD,oBAAoB;oBACpB,WAAW;oBACX,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,eAAe,IAAI,OAAO,WAAW;oBACvC;gBACF;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,uBAA2D;oBACtE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,uBAA2D;oBACtE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAIA;;GAEC,GACD,aAAa,cAAc,QAAgB,EAAiB;oBAC1D,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CAAO,kBAAkB;wBAC/D;oBACF;oBACA,OAAO,SAAS,IAAI;gBACtB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC8BF,kBAAkB;2BAAlB;;gBAOb,mCAAmC;gBAEnC,OASE;2BATF;;gBAxLa,cAAc;2BAAd;;gBAuIA,SAAS;2BAAT;;gBASA,QAAQ;2BAAR;;gBAWA,gBAAgB;2BAAhB;;gBAtCA,WAAW;2BAAX;;gBASA,WAAW;2BAAX;;;;;yCAzNyB;wCACd;kDAC8B;6CAE1B;;;;;;;;;;sBAOhB;gBACV,iBAAiB;gBAEjB,wBAAwB;gBAExB,sBAAsB;gBAEtB,qBAAqB;gBAErB,YAAY;eATF,qBAAA;YA6BZ;;CAEC,GACD,MAAM,uBAA2D;gBAC/D,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,YAAY,CAAC,EAAE;oBAC3B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,QAAQ,CAAC,EAAE;oBACvB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,oBAAoB,CAAC,EAAE;oBACnC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,iBAAiB,CAAC,EAAE;oBAChC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,qBAAqB,CAAC,EAAE;oBACpC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,mBAAmB,CAAC,EAAE;oBAClC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,eAAe,CAAC,EAAE;oBAC9B,WAAW;oBACX,eAAe;gBACjB;YACF;YAUO,MAAM,iBAAiB,CAC5B,UACA;gBAEA,IAAI,CAAC,YAAY,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACnD;gBAGF,MAAM,YAAY,SAAS,IAAI;gBAC/B,MAAM,eAAe,SAAS,OAAO,IAAI,IAAA,6BAAc,EAAC;gBAExD,OAAO;gBACP,QAAQ,GAAG,CAAC,uBAAuB;oBACjC;oBACA;oBACA;oBACA;gBACF;gBAEA,OAAO;gBACP,MAAM,gBAAgB,oBAAoB,CAAC,UAAU,IAAI;oBACvD,WAAW;gBACb;gBACA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,eAAe;gBACf,MAAM,iBAAiB,YAAY,aAAa,IAAI;gBAEpD,QAAQ,GAAG,CAAC,WAAW;oBACrB;oBACA;oBACA;gBACF;gBAEA,SAAS;gBACT,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC;oBAAE,MAAM;oBAAW,SAAS;oBAAc;gBAAS;gBAGzE,aAAa;gBACb,OAAQ,YAAY,WAAW;oBAC7B;wBACE,QAAQ,GAAG,CAAC;wBACZ;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,OAAO,CAAC;wBAChB;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;oBAEF;wBACE,QAAQ,GAAG,CAAC,SAAS;wBACrB,kBAAY,CAAC,KAAK,CAAC;4BACjB,SAAS;4BACT,aAAa;4BACb,UAAU;wBACZ;wBACA;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW,WAAW;wBAClC,gBAAgB,WAAW;wBAC3B;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;gBACJ;YACF;YAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,WAAmB;gBAC1C,QAAQ,GAAG,CAAC,wBAAwB;oBAAE;oBAAW;gBAAa;gBAE9D,IAAI,cAAc,2BAAY,CAAC,YAAY,EAAE;oBAC3C,qBAAqB;oBACrB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oBAEzB,QAAQ,GAAG,CAAC,YAAY;wBAAE;wBAAa;oBAAmB;oBAE1D,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb;oBACF;oBAEA,gBAAgB;oBAChB,qBAAW,CAAC,WAAW;oBACvB,QAAQ,GAAG,CAAC,oBAAoB;oBAChC,aAAO,CAAC,KAAK,CAAC,gBAAgB;oBAC9B,YAAO,CAAC,IAAI,CAAC;gBACf,OAAO,IAAI,cAAc,2BAAY,CAAC,SAAS,EAAE;oBAC/C,QAAQ,GAAG,CAAC,cAAc;oBAC1B,aAAO,CAAC,KAAK,CAAC,gBAAgB;gBAChC;YACF;YASO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,YAAY,CAAC;gBACxB,aAAO,CAAC,KAAK,CAAC;YAChB;YAOO,MAAM,WAAW,CAAC;gBACvB,aAAO,CAAC,IAAI,CAAC;YACf;YASO,MAAM,mBAAmB,CAC9B,OACA,aACA,OAAiD,MAAM;gBAEvD,kBAAY,CAAC,KAAK,CAAC;oBACjB,SAAS;oBACT;oBACA,UAAU;gBACZ;YACF;YAUO,MAAM,qBAAqB,CAAC;gBACjC,OAAO,CAAC,UAA4B;oBAClC,MAAM,cAAc;wBAAE,GAAG,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAClD,eAAe,UAAU;gBAC3B;YACF;gBAIA,WAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCnGa,SAAS;2BAAT;;gBAnIA,WAAW;2BAAX;;gBA1CA,WAAW;2BAAX;;gBAiJA,kBAAkB;2BAAlB;;gBA7HA,QAAQ;2BAAR;;gBAoJA,WAAW;2BAAX;;gBA9JA,SAAS;2BAAT;;gBA0CA,eAAe;2BAAf;;gBAfA,qBAAqB;2BAArB;;gBAtBA,SAAS;2BAAT;;gBAqKA,YAAY;2BAAZ;;gBAtBA,kBAAkB;2BAAlB;;gBA/GA,mBAAmB;2BAAnB;;gBAtDA,OAAO;2BAAP;;gBAmJA,aAAa;2BAAb;;gBA9GA,iBAAiB;2BAAjB;;gBAzBA,YAAY;2BAAZ;;gBAoBA,oBAAoB;2BAApB;;gBA6Lb,OAA4B;2BAA5B;;gBA9Ga,cAAc;2BAAd;;gBAhCA,aAAa;2BAAb;;gBAoBA,OAAO;2BAAP;;gBAVA,aAAa;2BAAb;;gBApBA,SAAS;2BAAT;;;;;;;;;;;;;YArEN,MAAM,UAAU;YAOhB,MAAM,cAAc;YAKpB,MAAM,eAAe;YAKrB,MAAM,YAAY;YAKlB,MAAM,YAAY;YAKlB,MAAM,WAAW;YAKjB,MAAM,uBAAuB;YAK7B,MAAM,oBAAoB;YAO1B,MAAM,wBAAwB;YAK9B,MAAM,cAAc;YAKpB,MAAM,sBAAsB;YAK5B,MAAM,kBAAkB;YAUxB,MAAM,YAAY,CAAC;gBACxB,OAAO,SAAS;YAClB;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,UAAU,CAAC;gBACtB,OAAO,CAAC,UAAU;YACpB;YAUO,MAAM,iBAAiB,CAAC;gBAC7B,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE,OAAO;gBACX;YACF;YAOO,MAAM,gBAAgB;gBAAC;aAAQ;YAK/B,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,cAAc;mBAAI;mBAAuB;aAAmB;YAKlE,MAAM,YAAY;mBAAI;mBAAkB;aAAY;YAOpD,MAAM,eAAe;gBAC1B,QAAQ;gBACR;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IHzOD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}