globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/user.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                UserService: function() {
                    return UserService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class UserService {
                /**
   * 获取当前用户资料
   *
   * 获取当前登录用户的基本资料信息，包括姓名、邮箱、电话等。
   * 需要有效的用户Token。
   *
   * @returns Promise<UserProfileResponse> 用户资料信息
   * @throws 当用户未登录或Token无效时抛出异常
   *
   * @example
   * ```typescript
   * const profile = await UserService.getUserProfile();
   * console.log('用户姓名:', profile.name);
   * console.log('用户邮箱:', profile.email);
   * ```
   */ static async getUserProfile() {
                    const response = await _request.apiRequest.get('/users/profile');
                    return response.data;
                }
                /**
   * 更新用户资料
   *
   * 更新当前用户的资料信息。可以更新姓名、电话、职位等信息。
   * 邮箱通常不允许修改，因为它是用户的唯一标识。
   *
   * @param data 用户资料更新请求参数
   * @param data.name 用户姓名
   * @param data.telephone 电话号码（可选）
   * @param data.position 职位（可选）
   * @returns Promise<UserProfileResponse> 更新后的用户资料
   * @throws 当数据验证失败或用户无权限时抛出异常
   *
   * @example
   * ```typescript
   * const updatedProfile = await UserService.updateUserProfile({
   *   name: '张三',
   *   telephone: '13800138000',
   *   position: '项目经理'
   * });
   * console.log('资料更新成功:', updatedProfile);
   * ```
   */ static async updateUserProfile(data) {
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 修改密码
   *
   * 修改当前用户的登录密码。需要提供当前密码进行验证。
   *
   * @param currentPassword 当前密码
   * @param newPassword 新密码（8-20位，包含字母和数字）
   * @returns Promise<void> 修改成功时resolve
   * @throws 当当前密码错误或新密码格式不正确时抛出异常
   *
   * @example
   * ```typescript
   * await UserService.changePassword('oldPassword123', 'newPassword456');
   * console.log('密码修改成功');
   * ```
   */ static async changePassword(currentPassword, newPassword) {
                    const data = {
                        currentPassword,
                        newPassword
                    };
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 更新用户名
   */ static async updateUserName(name) {
                    const data = {
                        name
                    };
                    const response = await _request.apiRequest.put('/users/profile', data);
                    return response.data;
                }
                /**
   * 验证当前密码
   */ static async validateCurrentPassword(password) {
                    try {
                        const response = await _request.apiRequest.post('/users/validate-password', {
                            password
                        });
                        return response.data;
                    } catch  {
                        return false;
                    }
                }
                /**
   * 获取用户统计信息
   *
   * 获取用户的团队统计信息，包括总团队数、创建的团队数等。
   * 注意：当前返回模拟数据，等待后端实现专门的统计接口。
   *
   * @returns Promise<object> 用户统计信息
   * @returns Promise<object>.totalTeams 总团队数
   * @returns Promise<object>.createdTeams 创建的团队数
   * @returns Promise<object>.joinedTeams 加入的团队数
   * @returns Promise<object>.lastLoginTime 最后登录时间
   *
   * @example
   * ```typescript
   * const stats = await UserService.getUserStats();
   * console.log('总团队数:', stats.totalTeams);
   * console.log('创建的团队:', stats.createdTeams);
   * ```
   */ static async getUserStats() {
                    // 这里可能需要后端提供专门的统计接口
                    // 暂时返回模拟数据
                    return {
                        totalTeams: 0,
                        createdTeams: 0,
                        joinedTeams: 0,
                        lastLoginTime: new Date().toISOString()
                    };
                }
                /**
   * 获取用户个人统计数据
   *
   * 获取用户在当前团队中的业务统计数据，包括车辆、人员、预警、告警等数量。
   * 这些数据用于个人中心的统计卡片显示。
   *
   * @returns Promise<UserPersonalStatsResponse> 个人统计数据
   * @throws 当用户未选择团队或无权限时抛出异常
   *
   * @example
   * ```typescript
   * const personalStats = await UserService.getUserPersonalStats();
   * console.log('车辆数量:', personalStats.vehicles);
   * console.log('人员数量:', personalStats.personnel);
   * console.log('预警数量:', personalStats.warnings);
   * console.log('告警数量:', personalStats.alerts);
   * ```
   */ static async getUserPersonalStats() {
                    const response = await _request.apiRequest.get('/users/personal-stats');
                    return response.data;
                }
                /**
   * 获取用户详细信息
   *
   * 获取用户的详细资料信息，包括基本信息、注册时间、最后登录信息等。
   * 比getUserProfile返回更多的详细信息，用于个人中心展示。
   *
   * @returns Promise<UserProfileDetailResponse> 用户详细信息
   * @throws 当用户未登录或Token无效时抛出异常
   *
   * @example
   * ```typescript
   * const detail = await UserService.getUserProfileDetail();
   * console.log('注册时间:', detail.registerDate);
   * console.log('最后登录:', detail.lastLoginTime);
   * console.log('团队数量:', detail.teamCount);
   * ```
   */ static async getUserProfileDetail() {
                    const response = await _request.apiRequest.get('/users/profile-detail');
                    return response.data;
                }
                /**
   * 删除用户账户
   */ static async deleteAccount(password) {
                    const response = await _request.apiRequest.delete('/users/account', {
                        password
                    });
                    return response.data;
                }
            }
            var _default = UserService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '3551152474990421111';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.9170271992821750441.hot-update.js.map