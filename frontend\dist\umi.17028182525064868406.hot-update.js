globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/team.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TeamService: function() {
                    return TeamService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TeamService {
                /**
   * 创建团队
   *
   * 创建新的团队，创建者自动成为团队管理员。
   * 需要用户级别的Token（Account Token）。
   *
   * @param data 团队创建请求参数
   * @param data.name 团队名称（必填，2-50字符）
   * @param data.description 团队描述（可选）
   * @returns Promise<TeamDetailResponse> 创建的团队信息
   * @throws 当团队名称重复或用户权限不足时抛出异常
   *
   * @example
   * ```typescript
   * const newTeam = await TeamService.createTeam({
   *   name: '开发团队',
   *   description: '负责产品开发的团队'
   * });
   * console.log('团队创建成功:', newTeam.name);
   * ```
   */ static async createTeam(data) {
                    const response = await _request.apiRequest.post('/teams', data);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 获取用户的团队列表
   *
   * 获取当前用户所属的所有团队的基本信息。
   * 需要用户级别的Token（Account Token）。
   *
   * @returns Promise<TeamDetailResponse[]> 团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeams();
   * console.log('用户所属团队数量:', teams.length);
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, ID: ${team.id}`);
   * });
   * ```
   */ static async getUserTeams() {
                    const response = await _request.apiRequest.get('/teams');
                    return response.data;
                }
                /**
   * 获取用户的团队列表（包含统计数据）
   *
   * 获取当前用户所属的所有团队，包含每个团队的统计信息。
   * 用于个人中心的团队列表展示。
   *
   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeamsWithStats();
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);
   * });
   * ```
   */ static async getUserTeamsWithStats() {
                    const response = await _request.apiRequest.get('/teams?includeStats=true');
                    return response.data;
                }
                /**
   * 获取当前团队详情
   *
   * 获取当前选择团队的详细信息。
   * 需要团队级别的Token（Team Token）。
   *
   * @returns Promise<TeamDetailResponse> 团队详细信息
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamDetail = await TeamService.getCurrentTeamDetail();
   * console.log('当前团队:', teamDetail.name);
   * console.log('团队描述:', teamDetail.description);
   * console.log('成员数量:', teamDetail.memberCount);
   * ```
   */ static async getCurrentTeamDetail() {
                    const response = await _request.apiRequest.get('/teams/current');
                    return response.data;
                }
                /**
   * 更新当前团队信息
   *
   * 更新当前团队的基本信息，如名称、描述等。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 团队更新请求参数
   * @param data.name 新的团队名称（可选）
   * @param data.description 新的团队描述（可选）
   * @returns Promise<TeamDetailResponse> 更新后的团队信息
   * @throws 当用户非团队创建者或团队名称重复时抛出异常
   *
   * @example
   * ```typescript
   * const updatedTeam = await TeamService.updateCurrentTeam({
   *   name: '新团队名称',
   *   description: '更新后的团队描述'
   * });
   * console.log('团队信息更新成功');
   * ```
   */ static async updateCurrentTeam(data) {
                    const response = await _request.apiRequest.put('/teams/current', data);
                    return response.data;
                }
                /**
   * 删除当前团队（需要 Team Token，仅创建者）
   *
   * 权限要求：
   * - 需要有效的Team Token
   * - 只有团队创建者可以执行此操作
   *
   * 删除效果：
   * - 软删除团队记录
   * - 级联删除所有团队成员关系
   * - 不可恢复
   *
   * @returns Promise<void> 删除成功时resolve
   * @throws 当权限不足或团队不存在时抛出异常
   */ static async deleteCurrentTeam() {
                    await _request.apiRequest.delete('/teams/current');
                }
                /**
   * 获取当前团队成员列表（简单数组格式）
   *
   * 获取当前团队的所有成员，返回简单数组格式。
   * 内部调用分页接口并获取所有成员。
   *
   * @returns Promise<TeamMemberResponse[]> 团队成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const members = await TeamService.getCurrentTeamMembers();
   * console.log('团队成员数量:', members.length);
   * members.forEach(member => {
   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);
   * });
   * ```
   */ static async getCurrentTeamMembers() {
                    const response = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    return (response === null || response === void 0 ? void 0 : response.list) || [];
                }
                /**
   * 获取当前团队成员列表（分页格式）
   *
   * 获取当前团队的成员列表，支持分页查询。
   * 需要团队级别的Token（Team Token）。
   *
   * @param params 分页查询参数（可选）
   * @param params.current 当前页码（默认1）
   * @param params.pageSize 每页大小（默认10）
   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const membersPage = await TeamService.getTeamMembers({
   *   current: 1,
   *   pageSize: 20
   * });
   *
   * console.log('总成员数:', membersPage.total);
   * console.log('当前页成员:', membersPage.list);
   * ```
   */ static async getTeamMembers(params) {
                    const response = await _request.apiRequest.get('/teams/current/members', params);
                    return response.data;
                }
                /**
   * 邀请团队成员
   *
   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 邀请请求参数
   * @param data.emails 被邀请人的邮箱列表
   * @returns Promise<void> 邀请发送成功时resolve
   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.inviteMembers({
   *   emails: ['<EMAIL>', '<EMAIL>']
   * });
   * console.log('邀请已发送');
   * ```
   */ static async inviteMembers(data) {
                    const response = await _request.apiRequest.post('/teams/current/members/invite', data);
                    return response.data;
                }
                /**
   * 移除团队成员
   *
   * 从当前团队中移除指定成员。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 要移除的成员ID
   * @returns Promise<void> 移除成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.removeMember(123);
   * console.log('成员已移除');
   * ```
   */ static async removeMember(memberId) {
                    const response = await _request.apiRequest.delete(`/teams/current/members/${memberId}`);
                    return response.data;
                }
                /**
   * 更新团队成员状态
   *
   * 更新团队成员的激活状态（启用/禁用）。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 成员ID
   * @param isActive 是否激活（true=启用，false=禁用）
   * @returns Promise<void> 更新成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * // 禁用成员
   * await TeamService.updateMemberStatus(123, false);
   * console.log('成员已禁用');
   *
   * // 启用成员
   * await TeamService.updateMemberStatus(123, true);
   * console.log('成员已启用');
   * ```
   */ static async updateMemberStatus(memberId, isActive) {
                    const response = await _request.apiRequest.put(`/teams/current/members/${memberId}/status?isActive=${isActive}`);
                    return response.data;
                }
                /**
   * 获取团队统计信息
   *
   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。
   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。
   *
   * @returns Promise<object> 团队统计信息
   * @returns Promise<object>.memberCount 总成员数
   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）
   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const stats = await TeamService.getTeamStats();
   * console.log('总成员数:', stats.memberCount);
   * console.log('活跃成员数:', stats.activeMembers);
   * console.log('最近活跃成员数:', stats.recentActivity);
   * ```
   */ static async getTeamStats() {
                    // 这里可能需要后端提供专门的统计接口
                    // 暂时通过团队详情和成员列表来计算
                    const teamDetail = await TeamService.getCurrentTeamDetail();
                    const members = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    const activeMembers = members.list.filter((member)=>member.isActive).length;
                    const recentActivity = members.list.filter((member)=>{
                        const lastAccess = new Date(member.lastAccessTime);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return lastAccess > weekAgo;
                    }).length;
                    return {
                        memberCount: teamDetail.memberCount,
                        activeMembers,
                        recentActivity
                    };
                }
            }
            var _default = TeamService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/utils/errorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                createErrorHandler: function() {
                    return createErrorHandler;
                },
                // ============= 默认导出 =============
                default: function() {
                    return _default;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                showError: function() {
                    return showError;
                },
                showInfo: function() {
                    return showInfo;
                },
                showNotification: function() {
                    return showNotification;
                },
                showSuccess: function() {
                    return showSuccess;
                },
                showWarning: function() {
                    return showWarning;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                /** 静默处理，不显示任何消息 */ ErrorDisplayType["SILENT"] = "silent";
                /** 使用message.warning显示 */ ErrorDisplayType["WARNING"] = "warning";
                /** 使用message.error显示 */ ErrorDisplayType["ERROR"] = "error";
                /** 使用notification显示 */ ErrorDisplayType["NOTIFICATION"] = "notification";
                /** 重定向到登录页 */ ErrorDisplayType["REDIRECT"] = "redirect";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            /**
 * 默认错误处理配置
 */ const DEFAULT_ERROR_CONFIG = {
                [_responseCodes.ResponseCode.BAD_REQUEST]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.UNAUTHORIZED]: {
                    displayType: "redirect"
                },
                [_responseCodes.ResponseCode.FORBIDDEN]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.NOT_FOUND]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.CONFLICT]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.UNPROCESSABLE_ENTITY]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.TOO_MANY_REQUESTS]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.INTERNAL_SERVER_ERROR]: {
                    displayType: "error",
                    customMessage: '服务器内部错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.BAD_GATEWAY]: {
                    displayType: "error",
                    customMessage: '网关错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.SERVICE_UNAVAILABLE]: {
                    displayType: "error",
                    customMessage: '服务暂时不可用，请稍后重试'
                },
                [_responseCodes.ResponseCode.GATEWAY_TIMEOUT]: {
                    displayType: "error",
                    customMessage: '请求超时，请稍后重试'
                }
            };
            const handleApiError = (response, config)=>{
                if (!response || _responseCodes.ResponseCode.isSuccess(response.code)) return;
                const errorCode = response.code;
                const errorMessage = response.message || (0, _responseCodes.getDescription)(errorCode);
                // 调试日志
                console.log('handleApiError 被调用:', {
                    errorCode,
                    errorMessage,
                    response,
                    config
                });
                // 合并配置
                const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {
                    displayType: "error"
                };
                const finalConfig = {
                    ...defaultConfig,
                    ...config
                };
                // 使用自定义消息或响应消息
                const displayMessage = finalConfig.customMessage || errorMessage;
                console.log('错误处理配置:', {
                    defaultConfig,
                    finalConfig,
                    displayMessage
                });
                // 执行错误回调
                if (finalConfig.onError) finalConfig.onError({
                    code: errorCode,
                    message: errorMessage,
                    response
                });
                // 根据显示类型处理错误
                switch(finalConfig.displayType){
                    case "silent":
                        console.log('静默处理错误');
                        break;
                    case "warning":
                        console.log('显示警告消息:', displayMessage);
                        _antd.message.warning(displayMessage);
                        break;
                    case "error":
                        console.log('显示错误消息:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                    case "notification":
                        console.log('显示通知:', displayMessage);
                        _antd.notification.error({
                            message: '操作失败',
                            description: displayMessage,
                            duration: 4.5
                        });
                        break;
                    case "redirect":
                        console.log('处理认证错误:', errorCode, displayMessage);
                        handleAuthError(errorCode, displayMessage);
                        break;
                    default:
                        console.log('默认错误处理:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                }
            };
            /**
 * 处理认证相关错误
 * 
 * @param errorCode 错误代码
 * @param errorMessage 错误消息
 */ const handleAuthError = (errorCode, errorMessage)=>{
                console.log('handleAuthError 被调用:', {
                    errorCode,
                    errorMessage
                });
                if (errorCode === _responseCodes.ResponseCode.UNAUTHORIZED) {
                    // 检查当前路径，避免在某些页面立即跳转
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    console.log('401错误处理:', {
                        currentPath,
                        isDashboardRelated
                    });
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        return;
                    }
                    // 清除认证信息并跳转到登录页
                    _services.AuthService.clearTokens();
                    console.log('显示401错误消息并跳转登录页:', errorMessage);
                    _antd.message.error(errorMessage || '登录已过期，请重新登录');
                    _max.history.push('/user/login');
                } else if (errorCode === _responseCodes.ResponseCode.FORBIDDEN) {
                    console.log('显示403错误消息:', errorMessage);
                    _antd.message.error(errorMessage || '没有权限访问该资源');
                }
            };
            const showSuccess = (msg)=>{
                _antd.message.success(msg);
            };
            const showWarning = (msg)=>{
                _antd.message.warning(msg);
            };
            const showError = (msg)=>{
                _antd.message.error(msg);
            };
            const showInfo = (msg)=>{
                _antd.message.info(msg);
            };
            const showNotification = (title, description, type = 'info')=>{
                _antd.notification[type]({
                    message: title,
                    description,
                    duration: 4.5
                });
            };
            const createErrorHandler = (defaultConfig)=>{
                return (response, config)=>{
                    const finalConfig = {
                        ...defaultConfig,
                        ...config
                    };
                    handleApiError(response, finalConfig);
                };
            };
            var _default = {
                handleApiError,
                showSuccess,
                showWarning,
                showError,
                showInfo,
                showNotification,
                createErrorHandler,
                ErrorDisplayType
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/constants/responseCodes.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ALL_CODES: function() {
                    return ALL_CODES;
                },
                BAD_GATEWAY: function() {
                    return BAD_GATEWAY;
                },
                BAD_REQUEST: function() {
                    return BAD_REQUEST;
                },
                CLIENT_ERROR_CODES: function() {
                    return CLIENT_ERROR_CODES;
                },
                CONFLICT: function() {
                    return CONFLICT;
                },
                ERROR_CODES: function() {
                    return ERROR_CODES;
                },
                FORBIDDEN: function() {
                    return FORBIDDEN;
                },
                GATEWAY_TIMEOUT: function() {
                    return GATEWAY_TIMEOUT;
                },
                INTERNAL_SERVER_ERROR: function() {
                    return INTERNAL_SERVER_ERROR;
                },
                NOT_FOUND: function() {
                    return NOT_FOUND;
                },
                ResponseCode: function() {
                    return ResponseCode;
                },
                SERVER_ERROR_CODES: function() {
                    return SERVER_ERROR_CODES;
                },
                SERVICE_UNAVAILABLE: function() {
                    return SERVICE_UNAVAILABLE;
                },
                SUCCESS: function() {
                    return SUCCESS;
                },
                SUCCESS_CODES: function() {
                    return SUCCESS_CODES;
                },
                TOO_MANY_REQUESTS: function() {
                    return TOO_MANY_REQUESTS;
                },
                UNAUTHORIZED: function() {
                    return UNAUTHORIZED;
                },
                UNPROCESSABLE_ENTITY: function() {
                    return UNPROCESSABLE_ENTITY;
                },
                default: function() {
                    return _default;
                },
                getDescription: function() {
                    return getDescription;
                },
                isClientError: function() {
                    return isClientError;
                },
                isError: function() {
                    return isError;
                },
                isServerError: function() {
                    return isServerError;
                },
                isSuccess: function() {
                    return isSuccess;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const SUCCESS = 200;
            const BAD_REQUEST = 400;
            const UNAUTHORIZED = 401;
            const FORBIDDEN = 403;
            const NOT_FOUND = 404;
            const CONFLICT = 409;
            const UNPROCESSABLE_ENTITY = 422;
            const TOO_MANY_REQUESTS = 429;
            const INTERNAL_SERVER_ERROR = 500;
            const BAD_GATEWAY = 502;
            const SERVICE_UNAVAILABLE = 503;
            const GATEWAY_TIMEOUT = 504;
            const isSuccess = (code)=>{
                return code === SUCCESS;
            };
            const isClientError = (code)=>{
                return code >= 400 && code < 500;
            };
            const isServerError = (code)=>{
                return code >= 500 && code < 600;
            };
            const isError = (code)=>{
                return !isSuccess(code);
            };
            const getDescription = (code)=>{
                switch(code){
                    case SUCCESS:
                        return '操作成功';
                    case BAD_REQUEST:
                        return '请求参数错误';
                    case UNAUTHORIZED:
                        return '未认证，需要登录';
                    case FORBIDDEN:
                        return '权限不足';
                    case NOT_FOUND:
                        return '资源不存在';
                    case CONFLICT:
                        return '资源冲突';
                    case UNPROCESSABLE_ENTITY:
                        return '请求语义错误';
                    case TOO_MANY_REQUESTS:
                        return '请求频率过高';
                    case INTERNAL_SERVER_ERROR:
                        return '服务器内部错误';
                    case BAD_GATEWAY:
                        return '网关错误';
                    case SERVICE_UNAVAILABLE:
                        return '服务不可用';
                    case GATEWAY_TIMEOUT:
                        return '网关超时';
                    default:
                        return '未知错误';
                }
            };
            const SUCCESS_CODES = [
                SUCCESS
            ];
            const CLIENT_ERROR_CODES = [
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS
            ];
            const SERVER_ERROR_CODES = [
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT
            ];
            const ERROR_CODES = [
                ...CLIENT_ERROR_CODES,
                ...SERVER_ERROR_CODES
            ];
            const ALL_CODES = [
                ...SUCCESS_CODES,
                ...ERROR_CODES
            ];
            const ResponseCode = {
                // 成功状态码
                SUCCESS,
                // 客户端错误状态码
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS,
                // 服务器错误状态码
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT,
                // 工具方法
                isSuccess,
                isClientError,
                isServerError,
                isError,
                getDescription,
                // 代码集合
                SUCCESS_CODES,
                CLIENT_ERROR_CODES,
                SERVER_ERROR_CODES,
                ERROR_CODES,
                ALL_CODES
            };
            var _default = ResponseCode;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '3936984386682665707';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.17028182525064868406.hot-update.js.map