globalThis.makoModuleHotUpdate('p__user__login__index', {
    modules: {
        "src/pages/team/components/TeamListContent.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var _teamSelectionUtils = __mako_require__("src/utils/teamSelectionUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { Search } = _antd.Input;
            const TeamListContent = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teams, setTeams] = (0, _react.useState)([]);
                const [filteredTeams, setFilteredTeams] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [currentTeamId, setCurrentTeamId] = (0, _react.useState)(null);
                (0, _react.useEffect)(()=>{
                    fetchTeams();
                    const teamId = (0, _tokenUtils.getTeamIdFromCurrentToken)();
                    setCurrentTeamId(teamId);
                }, []);
                (0, _react.useEffect)(()=>{
                    const filtered = teams.filter((team)=>team.name.toLowerCase().includes(searchText.toLowerCase()) || team.description && team.description.toLowerCase().includes(searchText.toLowerCase()));
                    setFilteredTeams(filtered);
                }, [
                    teams,
                    searchText
                ]);
                const fetchTeams = async ()=>{
                    try {
                        setLoading(true);
                        const teamList = await _services.TeamService.getUserTeams();
                        setTeams(teamList);
                    } catch (error) {
                        console.error('获取团队列表失败:', error);
                        _antd.message.error('获取团队列表失败');
                    } finally{
                        setLoading(false);
                    }
                };
                const handleCreateTeam = ()=>{
                    _max.history.push('/personal-center');
                };
                const handleViewTeam = async (team)=>{
                    try {
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                            _antd.message.success(`已切换到团队：${team.name}`);
                            setCurrentTeamId(team.id);
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('切换团队失败:', error);
                    }
                };
                const handleSwitchTeam = async (team)=>{
                    try {
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                            _antd.message.success(`已切换到团队：${team.name}`);
                            const currentUserId = (0, _tokenUtils.getUserIdFromCurrentToken)();
                            if (currentUserId) (0, _teamSelectionUtils.recordTeamSelection)(currentUserId, team.id);
                            setCurrentTeamId(team.id);
                            setTimeout(()=>{
                                _max.history.push('/');
                            }, 200);
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('切换团队失败:', error);
                    }
                };
                return (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(Search, {
                                placeholder: "搜索团队名称或描述",
                                allowClear: true,
                                value: searchText,
                                onChange: (e)=>setSearchText(e.target.value),
                                style: {
                                    width: 300
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 141,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 140,
                            columnNumber: 7
                        }, this),
                        filteredTeams.length === 0 && !loading ? (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                            image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                            description: searchText ? '没有找到匹配的团队' : '您还没有加入任何团队',
                            children: !searchText && (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                onClick: handleCreateTeam,
                                children: "创建第一个团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 151,
                            columnNumber: 9
                        }, this) : (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            loading: loading,
                            itemLayout: "horizontal",
                            dataSource: filteredTeams,
                            pagination: {
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total)=>`共 ${total} 个团队`
                            },
                            renderItem: (team)=>{
                                const isCurrentTeam = currentTeamId === team.id;
                                return (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    style: {
                                        backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,
                                        border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,
                                        borderRadius: isCurrentTeam ? '8px' : undefined,
                                        padding: isCurrentTeam ? '16px' : undefined
                                    },
                                    actions: [
                                        (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 187,
                                                columnNumber: 27
                                            }, void 0),
                                            onClick: ()=>handleViewTeam(team),
                                            children: "查看详情"
                                        }, "view", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 184,
                                            columnNumber: 19
                                        }, void 0),
                                        isCurrentTeam ? (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: "green",
                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 196,
                                                columnNumber: 29
                                            }, void 0),
                                            children: "当前团队"
                                        }, "current", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 193,
                                            columnNumber: 21
                                        }, void 0) : (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            size: "small",
                                            onClick: ()=>handleSwitchTeam(team),
                                            children: "进入团队"
                                        }, "switch", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 201,
                                            columnNumber: 21
                                        }, void 0)
                                    ],
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                        avatar: (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 64,
                                            icon: (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 216,
                                                columnNumber: 29
                                            }, void 0),
                                            style: {
                                                backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',
                                                border: isCurrentTeam ? '2px solid #389e0d' : undefined
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 214,
                                            columnNumber: 21
                                        }, void 0),
                                        title: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 4,
                                                    style: {
                                                        margin: 0
                                                    },
                                                    children: team.name
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 225,
                                                    columnNumber: 23
                                                }, void 0),
                                                isCurrentTeam && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "green",
                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 229,
                                                        columnNumber: 50
                                                    }, void 0),
                                                    children: "当前团队"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 229,
                                                    columnNumber: 25
                                                }, void 0),
                                                team.isCreator && (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "gold",
                                                    icon: (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 234,
                                                        columnNumber: 49
                                                    }, void 0),
                                                    children: "创建者"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 224,
                                            columnNumber: 21
                                        }, void 0),
                                        description: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: "small",
                                            children: [
                                                team.description && (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    children: team.description
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 243,
                                                    columnNumber: 25
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: "small",
                                                            children: [
                                                                (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 247,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    children: [
                                                                        team.memberCount,
                                                                        " 名成员"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 248,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        team.assignedAt && (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "加入于 ",
                                                                new Date(team.assignedAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 251,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "创建于 ",
                                                                new Date(team.createdAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 255,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 23
                                                }, void 0),
                                                (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: "状态："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 260,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            color: team.isActive ? 'green' : 'red',
                                                            children: team.isActive ? '启用' : '停用'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 261,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        team.lastAccessTime && (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "最后访问：",
                                                                new Date(team.lastAccessTime).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 265,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 259,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 241,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 212,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                    lineNumber: 176,
                                    columnNumber: 15
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 164,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/components/TeamListContent.tsx",
                    lineNumber: 139,
                    columnNumber: 5
                }, this);
            };
            _s(TeamListContent, "oikW+lvAhQh6tcKAb30N1MiMeXY=");
            _c = TeamListContent;
            var _default = TeamListContent;
            var _c;
            $RefreshReg$(_c, "TeamListContent");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/user/login/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
            var _react = _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _components = __mako_require__("src/components/index.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _defaultSettings = _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            var _s1 = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const LoginFormComponent = _react.default.memo(_s(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading })=>{
                _s();
                const sendCodeButton = (0, _react.useMemo)(()=>(0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "link",
                        size: "small",
                        disabled: countdown > 0 || sendingCode,
                        loading: sendingCode,
                        onClick: handleSendCode,
                        style: {
                            padding: 0,
                            height: 'auto'
                        },
                        children: countdown > 0 ? `${countdown}s后重发` : '发送验证码'
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 37,
                        columnNumber: 5
                    }, this), [
                    countdown,
                    sendingCode,
                    handleSendCode
                ]);
                const emailField = (0, _react.useMemo)(()=>(0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "email",
                        rules: [
                            {
                                required: true,
                                message: '请输入邮箱！'
                            },
                            {
                                type: 'email',
                                message: '请输入有效的邮箱地址！'
                            }
                        ],
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 61,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "邮箱",
                            autoComplete: "email"
                        }, "email-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 59,
                            columnNumber: 7
                        }, this)
                    }, "email-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 51,
                        columnNumber: 5
                    }, this), []);
                const codeField = (0, _react.useMemo)(()=>(0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "code",
                        rules: [
                            {
                                required: true,
                                message: '请输入验证码！'
                            },
                            {
                                len: 6,
                                message: '验证码为6位数字！'
                            },
                            {
                                pattern: /^\d{6}$/,
                                message: '验证码只能包含数字！'
                            }
                        ],
                        children: (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 81,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "6位验证码",
                            maxLength: 6,
                            suffix: sendCodeButton
                        }, "code-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 79,
                            columnNumber: 7
                        }, this)
                    }, "code-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 70,
                        columnNumber: 5
                    }, this), [
                    sendCodeButton
                ]);
                return (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    name: "login",
                    size: "large",
                    onFinish: handleLogin,
                    autoComplete: "off",
                    children: [
                        emailField,
                        codeField,
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                textAlign: 'center'
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                style: {
                                    fontSize: '12px'
                                },
                                children: "新用户将自动完成注册并登录"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 102,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 101,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                htmlType: "submit",
                                loading: loading,
                                block: true,
                                children: "登录 / 注册"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 108,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 107,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 90,
                    columnNumber: 5
                }, this);
            }, "vH8AbKPV2dOQkAxU7xEfdsmzClM="));
            _c = LoginFormComponent;
            const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
                return {
                    container: {
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100vh',
                        overflow: 'auto',
                        backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
                        backgroundSize: '100% 100%'
                    },
                    content: {
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '32px 16px'
                    },
                    header: {
                        marginBottom: 40,
                        textAlign: 'center'
                    },
                    logo: {
                        marginBottom: 16
                    },
                    title: {
                        marginBottom: 0
                    },
                    loginCard: {
                        width: '100%',
                        maxWidth: 400,
                        boxShadow: token.boxShadowTertiary
                    },
                    footer: {
                        marginTop: 40,
                        textAlign: 'center'
                    },
                    lang: {
                        width: 42,
                        height: 42,
                        lineHeight: '42px',
                        position: 'fixed',
                        right: 16,
                        top: 16,
                        borderRadius: token.borderRadius,
                        ':hover': {
                            backgroundColor: token.colorBgTextHover
                        }
                    }
                };
            });
            const LoginPage = ()=>{
                _s1();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [sendingCode, setSendingCode] = (0, _react.useState)(false);
                const [countdown, setCountdown] = (0, _react.useState)(0);
                const [form] = _antd.Form.useForm();
                const { styles } = useStyles();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                (0, _react.useEffect)(()=>{
                    setCountdown(0);
                }, []);
                _react.default.useEffect(()=>{
                    let timer;
                    if (countdown > 0) timer = setTimeout(()=>{
                        setCountdown(countdown - 1);
                    }, 1000);
                    return ()=>{
                        if (timer) clearTimeout(timer);
                    };
                }, [
                    countdown
                ]);
                const handleSendCode = (0, _react.useCallback)(async (type = 'login')=>{
                    let email;
                    try {
                        await form.validateFields([
                            'email'
                        ]);
                        email = form.getFieldValue('email');
                        console.log('发送验证码前的邮箱值:', email);
                        if (!email) {
                            _antd.message.error('请输入邮箱地址');
                            return;
                        }
                    } catch (error) {
                        _antd.message.error('请输入有效的邮箱地址');
                        return;
                    }
                    setSendingCode(true);
                    try {
                        const request = {
                            email,
                            type
                        };
                        const response = await _services.AuthService.sendVerificationCode(request);
                        if (response.success) {
                            _antd.message.success(response.message);
                            setCountdown(60);
                            console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));
                            _antd.message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);
                        } else {
                            _antd.message.error(response.message);
                            if (response.nextSendTime) setCountdown(response.nextSendTime);
                        }
                    } catch (error) {
                        console.error('发送验证码失败:', error);
                        _antd.message.error('发送验证码失败，请稍后重试');
                    } finally{
                        setSendingCode(false);
                    }
                }, [
                    form
                ]);
                const handleLogin = (0, _react.useCallback)(async (values)=>{
                    setLoading(true);
                    try {
                        const response = await _services.AuthService.login(values);
                        _antd.message.success('登录成功！');
                        setCountdown(0);
                        await setInitialState((prevState)=>({
                                ...prevState,
                                currentUser: response.user,
                                currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                            }));
                        if (response.teams.length === 0) _max.history.push('/personal-center');
                        else _max.history.push('/personal-center', {
                            teams: response.teams
                        });
                    } catch (error) {
                        console.error('登录失败:', error);
                    } finally{
                        setLoading(false);
                    }
                }, [
                    setInitialState
                ]);
                return (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.container,
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                            children: (0, _jsxdevruntime.jsxDEV)("title", {
                                children: [
                                    "登录 / 注册",
                                    _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 286,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 285,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.content,
                            children: [
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.header,
                                    children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        align: "center",
                                        size: "large",
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.logo,
                                                children: (0, _jsxdevruntime.jsxDEV)("img", {
                                                    src: "/logo.svg",
                                                    alt: "TeamAuth",
                                                    height: 48
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 294,
                                                columnNumber: 13
                                            }, this),
                                            (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.title,
                                                children: [
                                                    (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        children: "团队管理系统"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 298,
                                                        columnNumber: 15
                                                    }, this),
                                                    (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: "现代化的团队协作与管理平台"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 299,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 297,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 293,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 292,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: styles.loginCard,
                                    children: (0, _jsxdevruntime.jsxDEV)(LoginFormComponent, {
                                        form: form,
                                        handleLogin: handleLogin,
                                        handleSendCode: ()=>handleSendCode('login'),
                                        sendingCode: sendingCode,
                                        countdown: countdown,
                                        loading: loading
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 305,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 304,
                                    columnNumber: 9
                                }, this),
                                (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.footer,
                                    children: (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "© 2025 TeamAuth. All rights reserved."
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 316,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 315,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 291,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 319,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 284,
                    columnNumber: 5
                }, this);
            };
            _s1(LoginPage, "Yq4Fb8fQ18048Y8KY3QhqRjuF9Y=", false, function() {
                return [
                    _antd.Form.useForm,
                    useStyles,
                    _max.useModel
                ];
            });
            _c1 = LoginPage;
            var _default = LoginPage;
            var _c;
            var _c1;
            $RefreshReg$(_c, "LoginFormComponent");
            $RefreshReg$(_c1, "LoginPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8134896505261494410';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=p__user__login__index-async.14442352024696127844.hot-update.js.map