globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/services/todo.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "TodoService", {
                enumerable: true,
                get: function() {
                    return TodoService;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TodoService {
                static async getUserTodos() {
                    const response = await _request.apiRequest.get('/todos');
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                static async createTodo(request) {
                    const response = await _request.apiRequest.post('/todos', request);
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                static async updateTodo(id, request) {
                    const response = await _request.apiRequest.put(`/todos/${id}`, request);
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                static async deleteTodo(id) {
                    const response = await _request.apiRequest.delete(`/todos/${id}`);
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                }
                static async getTodoStats() {
                    const response = await _request.apiRequest.get('/todos/stats');
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/constants/responseCodes.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ALL_CODES: function() {
                    return ALL_CODES;
                },
                BAD_GATEWAY: function() {
                    return BAD_GATEWAY;
                },
                BAD_REQUEST: function() {
                    return BAD_REQUEST;
                },
                CLIENT_ERROR_CODES: function() {
                    return CLIENT_ERROR_CODES;
                },
                CONFLICT: function() {
                    return CONFLICT;
                },
                ERROR_CODES: function() {
                    return ERROR_CODES;
                },
                FORBIDDEN: function() {
                    return FORBIDDEN;
                },
                GATEWAY_TIMEOUT: function() {
                    return GATEWAY_TIMEOUT;
                },
                INTERNAL_SERVER_ERROR: function() {
                    return INTERNAL_SERVER_ERROR;
                },
                NOT_FOUND: function() {
                    return NOT_FOUND;
                },
                ResponseCode: function() {
                    return ResponseCode;
                },
                SERVER_ERROR_CODES: function() {
                    return SERVER_ERROR_CODES;
                },
                SERVICE_UNAVAILABLE: function() {
                    return SERVICE_UNAVAILABLE;
                },
                SUCCESS: function() {
                    return SUCCESS;
                },
                SUCCESS_CODES: function() {
                    return SUCCESS_CODES;
                },
                TOO_MANY_REQUESTS: function() {
                    return TOO_MANY_REQUESTS;
                },
                UNAUTHORIZED: function() {
                    return UNAUTHORIZED;
                },
                UNPROCESSABLE_ENTITY: function() {
                    return UNPROCESSABLE_ENTITY;
                },
                default: function() {
                    return _default;
                },
                getDescription: function() {
                    return getDescription;
                },
                isClientError: function() {
                    return isClientError;
                },
                isError: function() {
                    return isError;
                },
                isServerError: function() {
                    return isServerError;
                },
                isSuccess: function() {
                    return isSuccess;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const SUCCESS = 200;
            const BAD_REQUEST = 400;
            const UNAUTHORIZED = 401;
            const FORBIDDEN = 403;
            const NOT_FOUND = 404;
            const CONFLICT = 409;
            const UNPROCESSABLE_ENTITY = 422;
            const TOO_MANY_REQUESTS = 429;
            const INTERNAL_SERVER_ERROR = 500;
            const BAD_GATEWAY = 502;
            const SERVICE_UNAVAILABLE = 503;
            const GATEWAY_TIMEOUT = 504;
            const isSuccess = (code)=>{
                return code === SUCCESS;
            };
            const isClientError = (code)=>{
                return code >= 400 && code < 500;
            };
            const isServerError = (code)=>{
                return code >= 500 && code < 600;
            };
            const isError = (code)=>{
                return !isSuccess(code);
            };
            const getDescription = (code)=>{
                switch(code){
                    case SUCCESS:
                        return '操作成功';
                    case BAD_REQUEST:
                        return '请求参数错误';
                    case UNAUTHORIZED:
                        return '未认证，需要登录';
                    case FORBIDDEN:
                        return '权限不足';
                    case NOT_FOUND:
                        return '资源不存在';
                    case CONFLICT:
                        return '资源冲突';
                    case UNPROCESSABLE_ENTITY:
                        return '请求语义错误';
                    case TOO_MANY_REQUESTS:
                        return '请求频率过高';
                    case INTERNAL_SERVER_ERROR:
                        return '服务器内部错误';
                    case BAD_GATEWAY:
                        return '网关错误';
                    case SERVICE_UNAVAILABLE:
                        return '服务不可用';
                    case GATEWAY_TIMEOUT:
                        return '网关超时';
                    default:
                        return '未知错误';
                }
            };
            const SUCCESS_CODES = [
                SUCCESS
            ];
            const CLIENT_ERROR_CODES = [
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS
            ];
            const SERVER_ERROR_CODES = [
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT
            ];
            const ERROR_CODES = [
                ...CLIENT_ERROR_CODES,
                ...SERVER_ERROR_CODES
            ];
            const ALL_CODES = [
                ...SUCCESS_CODES,
                ...ERROR_CODES
            ];
            const ResponseCode = {
                SUCCESS,
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS,
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT,
                isSuccess,
                isClientError,
                isServerError,
                isError,
                getDescription,
                SUCCESS_CODES,
                CLIENT_ERROR_CODES,
                SERVER_ERROR_CODES,
                ERROR_CODES,
                ALL_CODES
            };
            var _default = ResponseCode;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/utils/errorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                createErrorHandler: function() {
                    return createErrorHandler;
                },
                default: function() {
                    return _default;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                showError: function() {
                    return showError;
                },
                showInfo: function() {
                    return showInfo;
                },
                showNotification: function() {
                    return showNotification;
                },
                showSuccess: function() {
                    return showSuccess;
                },
                showWarning: function() {
                    return showWarning;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                ErrorDisplayType["SILENT"] = "silent";
                ErrorDisplayType["WARNING"] = "warning";
                ErrorDisplayType["ERROR"] = "error";
                ErrorDisplayType["NOTIFICATION"] = "notification";
                ErrorDisplayType["REDIRECT"] = "redirect";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            const DEFAULT_ERROR_CONFIG = {
                [_responseCodes.ResponseCode.BAD_REQUEST]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.UNAUTHORIZED]: {
                    displayType: "redirect"
                },
                [_responseCodes.ResponseCode.FORBIDDEN]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.NOT_FOUND]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.CONFLICT]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.UNPROCESSABLE_ENTITY]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.TOO_MANY_REQUESTS]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.INTERNAL_SERVER_ERROR]: {
                    displayType: "error",
                    customMessage: '服务器内部错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.BAD_GATEWAY]: {
                    displayType: "error",
                    customMessage: '网关错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.SERVICE_UNAVAILABLE]: {
                    displayType: "error",
                    customMessage: '服务暂时不可用，请稍后重试'
                },
                [_responseCodes.ResponseCode.GATEWAY_TIMEOUT]: {
                    displayType: "error",
                    customMessage: '请求超时，请稍后重试'
                }
            };
            const handleApiError = (response, config)=>{
                if (!response || _responseCodes.ResponseCode.isSuccess(response.code)) return;
                const errorCode = response.code;
                const errorMessage = response.message || (0, _responseCodes.getDescription)(errorCode);
                console.log('handleApiError 被调用:', {
                    errorCode,
                    errorMessage,
                    response,
                    config
                });
                const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {
                    displayType: "error"
                };
                const finalConfig = {
                    ...defaultConfig,
                    ...config
                };
                const displayMessage = finalConfig.customMessage || errorMessage;
                console.log('错误处理配置:', {
                    defaultConfig,
                    finalConfig,
                    displayMessage
                });
                if (finalConfig.onError) finalConfig.onError({
                    code: errorCode,
                    message: errorMessage,
                    response
                });
                switch(finalConfig.displayType){
                    case "silent":
                        console.log('静默处理错误');
                        break;
                    case "warning":
                        console.log('显示警告消息:', displayMessage);
                        _antd.message.warning(displayMessage);
                        break;
                    case "error":
                        console.log('显示错误消息:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                    case "notification":
                        console.log('显示通知:', displayMessage);
                        _antd.notification.error({
                            message: '操作失败',
                            description: displayMessage,
                            duration: 4.5
                        });
                        break;
                    case "redirect":
                        console.log('处理认证错误:', errorCode, displayMessage);
                        handleAuthError(errorCode, displayMessage);
                        break;
                    default:
                        console.log('默认错误处理:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                }
            };
            const handleAuthError = (errorCode, errorMessage)=>{
                console.log('handleAuthError 被调用:', {
                    errorCode,
                    errorMessage
                });
                if (errorCode === _responseCodes.ResponseCode.UNAUTHORIZED) {
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    console.log('401错误处理:', {
                        currentPath,
                        isDashboardRelated
                    });
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        return;
                    }
                    _services.AuthService.clearTokens();
                    console.log('显示401错误消息并跳转登录页:', errorMessage);
                    _antd.message.error(errorMessage || '登录已过期，请重新登录');
                    _max.history.push('/user/login');
                } else if (errorCode === _responseCodes.ResponseCode.FORBIDDEN) {
                    console.log('显示403错误消息:', errorMessage);
                    _antd.message.error(errorMessage || '没有权限访问该资源');
                }
            };
            const showSuccess = (msg)=>{
                _antd.message.success(msg);
            };
            const showWarning = (msg)=>{
                _antd.message.warning(msg);
            };
            const showError = (msg)=>{
                _antd.message.error(msg);
            };
            const showInfo = (msg)=>{
                _antd.message.info(msg);
            };
            const showNotification = (title, description, type = 'info')=>{
                _antd.notification[type]({
                    message: title,
                    description,
                    duration: 4.5
                });
            };
            const createErrorHandler = (defaultConfig)=>{
                return (response, config)=>{
                    const finalConfig = {
                        ...defaultConfig,
                        ...config
                    };
                    handleApiError(response, finalConfig);
                };
            };
            var _default = {
                handleApiError,
                showSuccess,
                showWarning,
                showError,
                showInfo,
                showNotification,
                createErrorHandler,
                ErrorDisplayType
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '10069871545040486165';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.1619100803811443971.hot-update.js.map