/**
 * API错误处理工具类单元测试
 */

import { message, notification } from 'antd';
import { history } from '@umijs/max';
import { AuthService } from '@/services';
import {
  ApiErrorHandler,
  ErrorDisplayType,
  apiErrorHandler,
  handleApiError,
  handleApiResponse,
  isApiError,
  extractErrorInfo,
} from '../apiErrorHandler';

// Mock dependencies
jest.mock('antd', () => ({
  message: {
    error: jest.fn(),
    warning: jest.fn(),
    success: jest.fn(),
  },
  notification: {
    error: jest.fn(),
  },
}));

jest.mock('@umijs/max', () => ({
  history: {
    push: jest.fn(),
  },
}));

jest.mock('@/services', () => ({
  AuthService: {
    clearTokens: jest.fn(),
  },
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test',
  },
  writable: true,
});

describe('ApiErrorHandler', () => {
  let errorHandler: ApiErrorHandler;

  beforeEach(() => {
    errorHandler = ApiErrorHandler.getInstance();
    jest.clearAllMocks();
    // Reset window location
    window.location.pathname = '/test';
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = ApiErrorHandler.getInstance();
      const instance2 = ApiErrorHandler.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('isApiError', () => {
    it('should return true for error responses', () => {
      const errorResponse = { code: 400, message: 'Bad Request' };
      expect(errorHandler.isApiError(errorResponse)).toBe(true);
    });

    it('should return false for success responses', () => {
      const successResponse = { code: 200, message: 'Success' };
      expect(errorHandler.isApiError(successResponse)).toBe(false);
    });

    it('should return false for responses without code', () => {
      const response = { data: 'some data' };
      expect(errorHandler.isApiError(response)).toBe(false);
    });
  });

  describe('extractErrorInfo', () => {
    it('should extract error info from error response', () => {
      const errorResponse = {
        code: 400,
        message: 'Bad Request',
        data: { field: 'error' },
        timestamp: '2023-01-01T00:00:00Z',
      };

      const errorInfo = errorHandler.extractErrorInfo(errorResponse);
      expect(errorInfo).toEqual({
        code: 400,
        message: 'Bad Request',
        data: { field: 'error' },
        timestamp: '2023-01-01T00:00:00Z',
        originalResponse: errorResponse,
      });
    });

    it('should return null for success response', () => {
      const successResponse = { code: 200, message: 'Success' };
      const errorInfo = errorHandler.extractErrorInfo(successResponse);
      expect(errorInfo).toBeNull();
    });

    it('should handle missing message field', () => {
      const errorResponse = { code: 400 };
      const errorInfo = errorHandler.extractErrorInfo(errorResponse);
      expect(errorInfo?.message).toBe('未知错误');
    });
  });

  describe('handleError', () => {
    it('should handle authentication error (401)', () => {
      const errorInfo = {
        code: 401,
        message: 'Unauthorized',
      };

      const result = errorHandler.handleError(errorInfo);

      expect(AuthService.clearTokens).toHaveBeenCalled();
      expect(message.error).toHaveBeenCalledWith('登录已过期，请重新登录');
      expect(history.push).toHaveBeenCalledWith('/user/login');
      expect(result.handled).toBe(true);
      expect(result.shouldRedirect).toBe(true);
    });

    it('should handle dashboard authentication error differently', () => {
      window.location.pathname = '/dashboard/test';
      
      const errorInfo = {
        code: 401,
        message: 'Unauthorized',
      };

      const result = errorHandler.handleError(errorInfo);

      expect(AuthService.clearTokens).not.toHaveBeenCalled();
      expect(history.push).not.toHaveBeenCalled();
      expect(result.handled).toBe(true);
    });

    it('should handle permission error (403)', () => {
      const errorInfo = {
        code: 403,
        message: 'Forbidden',
      };

      const result = errorHandler.handleError(errorInfo);

      expect(message.error).toHaveBeenCalledWith('Forbidden');
      expect(result.handled).toBe(true);
    });

    it('should handle server error (5xx)', () => {
      const errorInfo = {
        code: 500,
        message: 'Internal Server Error',
      };

      const result = errorHandler.handleError(errorInfo);

      expect(message.error).toHaveBeenCalledWith('服务器内部错误，请稍后重试');
      expect(result.handled).toBe(true);
    });

    it('should handle different display types', () => {
      const errorInfo = {
        code: 400,
        message: 'Bad Request',
      };

      // Test WARNING display type
      errorHandler.handleError(errorInfo, {
        displayType: ErrorDisplayType.WARNING,
      });
      expect(message.warning).toHaveBeenCalledWith('Bad Request');

      // Test NOTIFICATION display type
      errorHandler.handleError(errorInfo, {
        displayType: ErrorDisplayType.NOTIFICATION,
      });
      expect(notification.error).toHaveBeenCalledWith({
        message: '操作失败',
        description: 'Bad Request',
        duration: 4.5,
      });

      // Test SILENT display type
      jest.clearAllMocks();
      errorHandler.handleError(errorInfo, {
        displayType: ErrorDisplayType.SILENT,
      });
      expect(message.error).not.toHaveBeenCalled();
      expect(message.warning).not.toHaveBeenCalled();
    });

    it('should use custom message when provided', () => {
      const errorInfo = {
        code: 400,
        message: 'Bad Request',
      };

      errorHandler.handleError(errorInfo, {
        customMessage: 'Custom error message',
      });

      expect(message.error).toHaveBeenCalledWith('Custom error message');
    });

    it('should execute custom handler', () => {
      const customHandler = jest.fn();
      const errorInfo = {
        code: 400,
        message: 'Bad Request',
      };

      errorHandler.handleError(errorInfo, {
        customHandler,
      });

      expect(customHandler).toHaveBeenCalledWith(errorInfo);
    });

    it('should skip default handler when configured', () => {
      const customHandler = jest.fn();
      const errorInfo = {
        code: 400,
        message: 'Bad Request',
      };

      errorHandler.handleError(errorInfo, {
        customHandler,
        skipDefaultHandler: true,
      });

      expect(customHandler).toHaveBeenCalledWith(errorInfo);
      expect(message.error).not.toHaveBeenCalled();
    });
  });

  describe('handleApiResponse', () => {
    it('should return null for success response', () => {
      const successResponse = {
        code: 200,
        message: 'Success',
        data: { result: 'ok' },
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = errorHandler.handleApiResponse(successResponse);
      expect(result).toBeNull();
    });

    it('should handle error response', () => {
      const errorResponse = {
        code: 400,
        message: 'Bad Request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = errorHandler.handleApiResponse(errorResponse);
      expect(result?.handled).toBe(true);
      expect(message.error).toHaveBeenCalledWith('Bad Request');
    });
  });

  describe('setDefaultConfig', () => {
    it('should update default configuration', () => {
      errorHandler.setDefaultConfig({
        displayType: ErrorDisplayType.WARNING,
        logError: false,
      });

      const errorInfo = {
        code: 400,
        message: 'Bad Request',
      };

      errorHandler.handleError(errorInfo);
      expect(message.warning).toHaveBeenCalledWith('Bad Request');
    });
  });

  describe('createErrorMiddleware', () => {
    it('should create middleware that handles errors', () => {
      const middleware = errorHandler.createErrorMiddleware({
        displayType: ErrorDisplayType.WARNING,
      });

      const errorResponse = {
        code: 400,
        message: 'Bad Request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = middleware(errorResponse);
      expect(result).toBe(errorResponse);
      expect(message.warning).toHaveBeenCalledWith('Bad Request');
    });
  });
});

describe('Convenience Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isApiError', () => {
    it('should work as convenience function', () => {
      expect(isApiError({ code: 400 })).toBe(true);
      expect(isApiError({ code: 200 })).toBe(false);
    });
  });

  describe('extractErrorInfo', () => {
    it('should work as convenience function', () => {
      const response = { code: 400, message: 'Error' };
      const errorInfo = extractErrorInfo(response);
      expect(errorInfo?.code).toBe(400);
    });
  });

  describe('handleApiError', () => {
    it('should work as convenience function', () => {
      const errorInfo = { code: 400, message: 'Bad Request' };
      const result = handleApiError(errorInfo);
      expect(result.handled).toBe(true);
      expect(message.error).toHaveBeenCalledWith('Bad Request');
    });
  });

  describe('handleApiResponse', () => {
    it('should work as convenience function', () => {
      const response = {
        code: 400,
        message: 'Bad Request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };
      const result = handleApiResponse(response);
      expect(result?.handled).toBe(true);
    });
  });
});
