globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/subscription.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                SubscriptionService: function() {
                    return SubscriptionService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class SubscriptionService {
                /**
   * 获取所有订阅套餐（公开接口）
   */ static async getAllPlans() {
                    const response = await _request.apiRequest.get('/plans');
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 获取活跃的订阅套餐
   */ static async getActivePlans() {
                    const allPlans = await SubscriptionService.getAllPlans();
                    return allPlans.filter((plan)=>plan.isActive);
                }
                /**
   * 根据 ID 获取订阅套餐详情
   */ static async getPlanById(planId) {
                    const allPlans = await SubscriptionService.getAllPlans();
                    const plan = allPlans.find((p)=>p.id === planId);
                    if (!plan) throw new Error('订阅套餐不存在');
                    return plan;
                }
                /**
   * 获取当前用户的有效订阅
   */ static async getCurrentSubscription() {
                    try {
                        const response = await _request.apiRequest.get('/subscriptions/current');
                        return response.data;
                    } catch (error) {
                        var _error_response;
                        // 如果没有订阅，返回 null
                        if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) return null;
                        throw error;
                    }
                }
                /**
   * 创建订阅
   */ static async createSubscription(data) {
                    const response = await _request.apiRequest.post('/subscriptions', data);
                    return response.data;
                }
                /**
   * 获取用户订阅列表
   */ static async getUserSubscriptions() {
                    const response = await _request.apiRequest.get('/subscriptions');
                    return response.data;
                }
                /**
   * 取消订阅
   */ static async cancelSubscription(subscriptionId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/cancel`);
                    return response.data;
                }
                /**
   * 续费订阅
   */ static async renewSubscription(subscriptionId, duration) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/renew`, {
                        duration
                    });
                    return response.data;
                }
                /**
   * 升级订阅套餐
   */ static async upgradeSubscription(subscriptionId, newPlanId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/upgrade`, {
                        planId: newPlanId
                    });
                    return response.data;
                }
                /**
   * 获取订阅使用统计
   */ static async getSubscriptionUsage() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        currentUsage: 0,
                        maxUsage: 0,
                        usagePercentage: 0,
                        remainingDays: 0
                    };
                    // 计算剩余天数
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / 86400000));
                    // 这里需要后端提供实际的使用量数据
                    // 暂时返回模拟数据
                    const currentUsage = 0; // 实际使用量
                    const maxUsage = currentSubscription.maxSize;
                    const usagePercentage = maxUsage > 0 ? currentUsage / maxUsage * 100 : 0;
                    return {
                        currentUsage,
                        maxUsage,
                        usagePercentage,
                        remainingDays
                    };
                }
                /**
   * 获取订阅历史记录
   */ static async getSubscriptionHistory() {
                    const subscriptions = await SubscriptionService.getUserSubscriptions();
                    // 按创建时间倒序排列
                    return subscriptions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                }
                /**
   * 计算套餐价格（考虑折扣等）
   */ static calculatePlanPrice(plan, duration) {
                    const originalPrice = plan.price * duration;
                    let discount = 0;
                    // 根据订阅时长给予折扣
                    if (duration >= 12) discount = 0.2; // 年付8折
                    else if (duration >= 6) discount = 0.1; // 半年付9折
                    const discountedPrice = originalPrice * (1 - discount);
                    return {
                        originalPrice,
                        discountedPrice,
                        discount: discount * 100,
                        totalPrice: discountedPrice
                    };
                }
                /**
   * 比较套餐功能
   */ static comparePlans(plans) {
                    return [
                        {
                            feature: '数据存储上限',
                            values: plans.map((plan)=>plan.maxSize)
                        },
                        {
                            feature: '月费价格',
                            values: plans.map((plan)=>`¥${plan.price}`)
                        },
                        {
                            feature: '技术支持',
                            values: plans.map((plan)=>plan.price > 0 ? '7x24小时' : '工作日')
                        },
                        {
                            feature: '数据备份',
                            values: plans.map((plan)=>plan.price > 0)
                        },
                        {
                            feature: '高级功能',
                            values: plans.map((plan)=>plan.price >= 100)
                        }
                    ];
                }
                /**
   * 检查订阅状态
   */ static async checkSubscriptionStatus() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        hasActiveSubscription: false,
                        isExpiringSoon: false,
                        daysUntilExpiry: 0,
                        needsUpgrade: false
                    };
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / 86400000);
                    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;
                    // 检查是否需要升级（基于使用量）
                    const usage = await SubscriptionService.getSubscriptionUsage();
                    const needsUpgrade = usage.usagePercentage > 80;
                    return {
                        hasActiveSubscription: true,
                        isExpiringSoon,
                        daysUntilExpiry,
                        needsUpgrade
                    };
                }
            }
            var _default = SubscriptionService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/constants/responseCodes.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ALL_CODES: function() {
                    return ALL_CODES;
                },
                BAD_GATEWAY: function() {
                    return BAD_GATEWAY;
                },
                BAD_REQUEST: function() {
                    return BAD_REQUEST;
                },
                CLIENT_ERROR_CODES: function() {
                    return CLIENT_ERROR_CODES;
                },
                CONFLICT: function() {
                    return CONFLICT;
                },
                ERROR_CODES: function() {
                    return ERROR_CODES;
                },
                FORBIDDEN: function() {
                    return FORBIDDEN;
                },
                GATEWAY_TIMEOUT: function() {
                    return GATEWAY_TIMEOUT;
                },
                INTERNAL_SERVER_ERROR: function() {
                    return INTERNAL_SERVER_ERROR;
                },
                NOT_FOUND: function() {
                    return NOT_FOUND;
                },
                ResponseCode: function() {
                    return ResponseCode;
                },
                SERVER_ERROR_CODES: function() {
                    return SERVER_ERROR_CODES;
                },
                SERVICE_UNAVAILABLE: function() {
                    return SERVICE_UNAVAILABLE;
                },
                SUCCESS: function() {
                    return SUCCESS;
                },
                SUCCESS_CODES: function() {
                    return SUCCESS_CODES;
                },
                TOO_MANY_REQUESTS: function() {
                    return TOO_MANY_REQUESTS;
                },
                UNAUTHORIZED: function() {
                    return UNAUTHORIZED;
                },
                UNPROCESSABLE_ENTITY: function() {
                    return UNPROCESSABLE_ENTITY;
                },
                default: function() {
                    return _default;
                },
                getDescription: function() {
                    return getDescription;
                },
                isClientError: function() {
                    return isClientError;
                },
                isError: function() {
                    return isError;
                },
                isServerError: function() {
                    return isServerError;
                },
                isSuccess: function() {
                    return isSuccess;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const SUCCESS = 200;
            const BAD_REQUEST = 400;
            const UNAUTHORIZED = 401;
            const FORBIDDEN = 403;
            const NOT_FOUND = 404;
            const CONFLICT = 409;
            const UNPROCESSABLE_ENTITY = 422;
            const TOO_MANY_REQUESTS = 429;
            const INTERNAL_SERVER_ERROR = 500;
            const BAD_GATEWAY = 502;
            const SERVICE_UNAVAILABLE = 503;
            const GATEWAY_TIMEOUT = 504;
            const isSuccess = (code)=>{
                return code === SUCCESS;
            };
            const isClientError = (code)=>{
                return code >= 400 && code < 500;
            };
            const isServerError = (code)=>{
                return code >= 500 && code < 600;
            };
            const isError = (code)=>{
                return !isSuccess(code);
            };
            const getDescription = (code)=>{
                switch(code){
                    case SUCCESS:
                        return '操作成功';
                    case BAD_REQUEST:
                        return '请求参数错误';
                    case UNAUTHORIZED:
                        return '未认证，需要登录';
                    case FORBIDDEN:
                        return '权限不足';
                    case NOT_FOUND:
                        return '资源不存在';
                    case CONFLICT:
                        return '资源冲突';
                    case UNPROCESSABLE_ENTITY:
                        return '请求语义错误';
                    case TOO_MANY_REQUESTS:
                        return '请求频率过高';
                    case INTERNAL_SERVER_ERROR:
                        return '服务器内部错误';
                    case BAD_GATEWAY:
                        return '网关错误';
                    case SERVICE_UNAVAILABLE:
                        return '服务不可用';
                    case GATEWAY_TIMEOUT:
                        return '网关超时';
                    default:
                        return '未知错误';
                }
            };
            const SUCCESS_CODES = [
                SUCCESS
            ];
            const CLIENT_ERROR_CODES = [
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS
            ];
            const SERVER_ERROR_CODES = [
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT
            ];
            const ERROR_CODES = [
                ...CLIENT_ERROR_CODES,
                ...SERVER_ERROR_CODES
            ];
            const ALL_CODES = [
                ...SUCCESS_CODES,
                ...ERROR_CODES
            ];
            const ResponseCode = {
                // 成功状态码
                SUCCESS,
                // 客户端错误状态码
                BAD_REQUEST,
                UNAUTHORIZED,
                FORBIDDEN,
                NOT_FOUND,
                CONFLICT,
                UNPROCESSABLE_ENTITY,
                TOO_MANY_REQUESTS,
                // 服务器错误状态码
                INTERNAL_SERVER_ERROR,
                BAD_GATEWAY,
                SERVICE_UNAVAILABLE,
                GATEWAY_TIMEOUT,
                // 工具方法
                isSuccess,
                isClientError,
                isServerError,
                isError,
                getDescription,
                // 代码集合
                SUCCESS_CODES,
                CLIENT_ERROR_CODES,
                SERVER_ERROR_CODES,
                ERROR_CODES,
                ALL_CODES
            };
            var _default = ResponseCode;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/utils/errorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                createErrorHandler: function() {
                    return createErrorHandler;
                },
                // ============= 默认导出 =============
                default: function() {
                    return _default;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                showError: function() {
                    return showError;
                },
                showInfo: function() {
                    return showInfo;
                },
                showNotification: function() {
                    return showNotification;
                },
                showSuccess: function() {
                    return showSuccess;
                },
                showWarning: function() {
                    return showWarning;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                /** 静默处理，不显示任何消息 */ ErrorDisplayType["SILENT"] = "silent";
                /** 使用message.warning显示 */ ErrorDisplayType["WARNING"] = "warning";
                /** 使用message.error显示 */ ErrorDisplayType["ERROR"] = "error";
                /** 使用notification显示 */ ErrorDisplayType["NOTIFICATION"] = "notification";
                /** 重定向到登录页 */ ErrorDisplayType["REDIRECT"] = "redirect";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            /**
 * 默认错误处理配置
 */ const DEFAULT_ERROR_CONFIG = {
                [_responseCodes.ResponseCode.BAD_REQUEST]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.UNAUTHORIZED]: {
                    displayType: "redirect"
                },
                [_responseCodes.ResponseCode.FORBIDDEN]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.NOT_FOUND]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.CONFLICT]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.UNPROCESSABLE_ENTITY]: {
                    displayType: "error"
                },
                [_responseCodes.ResponseCode.TOO_MANY_REQUESTS]: {
                    displayType: "warning"
                },
                [_responseCodes.ResponseCode.INTERNAL_SERVER_ERROR]: {
                    displayType: "error",
                    customMessage: '服务器内部错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.BAD_GATEWAY]: {
                    displayType: "error",
                    customMessage: '网关错误，请稍后重试'
                },
                [_responseCodes.ResponseCode.SERVICE_UNAVAILABLE]: {
                    displayType: "error",
                    customMessage: '服务暂时不可用，请稍后重试'
                },
                [_responseCodes.ResponseCode.GATEWAY_TIMEOUT]: {
                    displayType: "error",
                    customMessage: '请求超时，请稍后重试'
                }
            };
            const handleApiError = (response, config)=>{
                if (!response || _responseCodes.ResponseCode.isSuccess(response.code)) return;
                const errorCode = response.code;
                const errorMessage = response.message || (0, _responseCodes.getDescription)(errorCode);
                // 调试日志
                console.log('handleApiError 被调用:', {
                    errorCode,
                    errorMessage,
                    response,
                    config
                });
                // 合并配置
                const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {
                    displayType: "error"
                };
                const finalConfig = {
                    ...defaultConfig,
                    ...config
                };
                // 使用自定义消息或响应消息
                const displayMessage = finalConfig.customMessage || errorMessage;
                console.log('错误处理配置:', {
                    defaultConfig,
                    finalConfig,
                    displayMessage
                });
                // 执行错误回调
                if (finalConfig.onError) finalConfig.onError({
                    code: errorCode,
                    message: errorMessage,
                    response
                });
                // 根据显示类型处理错误
                switch(finalConfig.displayType){
                    case "silent":
                        console.log('静默处理错误');
                        break;
                    case "warning":
                        console.log('显示警告消息:', displayMessage);
                        _antd.message.warning(displayMessage);
                        break;
                    case "error":
                        console.log('显示错误消息:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                    case "notification":
                        console.log('显示通知:', displayMessage);
                        _antd.notification.error({
                            message: '操作失败',
                            description: displayMessage,
                            duration: 4.5
                        });
                        break;
                    case "redirect":
                        console.log('处理认证错误:', errorCode, displayMessage);
                        handleAuthError(errorCode, displayMessage);
                        break;
                    default:
                        console.log('默认错误处理:', displayMessage);
                        _antd.message.error(displayMessage);
                        break;
                }
            };
            /**
 * 处理认证相关错误
 * 
 * @param errorCode 错误代码
 * @param errorMessage 错误消息
 */ const handleAuthError = (errorCode, errorMessage)=>{
                console.log('handleAuthError 被调用:', {
                    errorCode,
                    errorMessage
                });
                if (errorCode === _responseCodes.ResponseCode.UNAUTHORIZED) {
                    // 检查当前路径，避免在某些页面立即跳转
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    console.log('401错误处理:', {
                        currentPath,
                        isDashboardRelated
                    });
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        return;
                    }
                    // 清除认证信息并跳转到登录页
                    _services.AuthService.clearTokens();
                    console.log('显示401错误消息并跳转登录页:', errorMessage);
                    _antd.message.error(errorMessage || '登录已过期，请重新登录');
                    _max.history.push('/user/login');
                } else if (errorCode === _responseCodes.ResponseCode.FORBIDDEN) {
                    console.log('显示403错误消息:', errorMessage);
                    _antd.message.error(errorMessage || '没有权限访问该资源');
                }
            };
            const showSuccess = (msg)=>{
                _antd.message.success(msg);
            };
            const showWarning = (msg)=>{
                _antd.message.warning(msg);
            };
            const showError = (msg)=>{
                _antd.message.error(msg);
            };
            const showInfo = (msg)=>{
                _antd.message.info(msg);
            };
            const showNotification = (title, description, type = 'info')=>{
                _antd.notification[type]({
                    message: title,
                    description,
                    duration: 4.5
                });
            };
            const createErrorHandler = (defaultConfig)=>{
                return (response, config)=>{
                    const finalConfig = {
                        ...defaultConfig,
                        ...config
                    };
                    handleApiError(response, finalConfig);
                };
            };
            var _default = {
                handleApiError,
                showSuccess,
                showWarning,
                showError,
                showInfo,
                showNotification,
                createErrorHandler,
                ErrorDisplayType
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '7174054161160553442';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.750508250767452519.hot-update.js.map