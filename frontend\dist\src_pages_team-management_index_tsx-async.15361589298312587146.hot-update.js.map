{"version": 3, "sources": ["src_pages_team-management_index_tsx-async.15361589298312587146.hot-update.js", "src/pages/team-management/components/TeamMemberManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/team-management/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='14699346167305758905';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队成员与邀请管理组件\n *\n * 功能特性：\n * - 统一页面显示团队成员列表和邀请记录\n * - 查看团队成员列表及详细信息\n * - 查看团队邀请列表及状态管理\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 取消待处理的邀请\n * - 批量操作支持\n * - 成员和邀请搜索筛选\n *\n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n *\n * 界面设计：\n * - 移除标签页导航，采用统一页面布局\n * - 团队成员和邀请记录垂直排列\n * - 提升用户体验和操作效率\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Typography,\n  Popconfirm,\n  Select,\n  Tooltip\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { TeamDetailResponse, TeamMemberResponse, TeamInvitationResponse } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  // 邀请管理相关状态\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [invitationLoading, setInvitationLoading] = useState(false);\n  const [invitationSearchText, setInvitationSearchText] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('');\n\n  useEffect(() => {\n    fetchMembers();\n    fetchInvitations();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setInvitationLoading(true);\n      const invitationList = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setInvitationLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      // 使用新的邀请链接功能\n      const response = await InvitationService.sendInvitations({\n        emails: emailList,\n        message: values.message\n      });\n\n      // Service layer handles errors, so if we get here, the request was successful\n      // Display detailed results\n      if (response.successCount > 0) {\n        message.success(`成功发送 ${response.successCount} 个邀请`);\n\n        // 显示邀请链接（可选：在开发环境中显示）\n        if (process.env.NODE_ENV === 'development') {\n          console.log('邀请链接:', response.invitations.map(inv => ({\n            email: inv.email,\n            link: inv.invitationLink\n          })));\n        }\n      }\n\n      if (response.failureCount > 0) {\n        message.warning(`${response.failureCount} 个邀请发送失败`);\n      }\n\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      fetchInvitations(); // 刷新邀请列表\n      onRefresh(); // 刷新团队详情\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请取消成功');\n      fetchInvitations();\n      onRefresh();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  // 邀请表格列定义\n  const invitationColumns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '被邀请人',\n      key: 'invitee',\n      render: (_, record) => (\n        <Space direction=\"vertical\" size={0}>\n          <Text strong>{record.inviteeName || '未注册用户'}</Text>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            <MailOutlined /> {record.inviteeEmail}\n          </Text>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent\n          status={status}\n          isExpired={record.isExpired}\n        />\n      ),\n      filters: [\n        { text: '待确认', value: InvitationStatus.PENDING },\n        { text: '已接受', value: InvitationStatus.ACCEPTED },\n        { text: '已拒绝', value: InvitationStatus.REJECTED },\n        { text: '已过期', value: InvitationStatus.EXPIRED },\n        { text: '已取消', value: InvitationStatus.CANCELLED },\n      ],\n      onFilter: (value, record) => record.status === value,\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => {\n        if (record.status === InvitationStatus.PENDING && !record.isExpired) {\n          return (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button size=\"small\" danger icon={<DeleteOutlined />}>\n                取消邀请\n              </Button>\n            </Popconfirm>\n          );\n        }\n        return <Text type=\"secondary\">-</Text>;\n      },\n    },\n  ];\n\n  // 过滤邀请列表\n  const filteredInvitations = invitations.filter(invitation => {\n    const matchesSearch = !invitationSearchText ||\n      invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) ||\n      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase()));\n\n    const matchesStatus = !statusFilter || invitation.status === statusFilter;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div>\n      {/* 团队成员管理区域 */}\n      <Card\n        title={\n          <Space>\n            <UserOutlined />\n            <span>团队成员 ({(members || []).length})</span>\n          </Space>\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {/* 成员操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 250 }}\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button danger icon={<DeleteOutlined />}>\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Space>\n        </div>\n\n        {/* 成员列表 */}\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请记录管理区域 */}\n      <Card\n        title={\n          <Space>\n            <MailOutlined />\n            <span>邀请记录 ({(invitations || []).length})</span>\n          </Space>\n        }\n      >\n        {/* 邀请操作栏 */}\n        <div style={{ marginBottom: 16 }}>\n          <Space style={{ width: '100%', justifyContent: 'space-between' }}>\n            <Space>\n              <Input\n                placeholder=\"搜索邮箱或姓名\"\n                prefix={<SearchOutlined />}\n                value={invitationSearchText}\n                onChange={(e) => setInvitationSearchText(e.target.value)}\n                style={{ width: 200 }}\n              />\n              <Select\n                placeholder=\"筛选状态\"\n                value={statusFilter}\n                onChange={setStatusFilter}\n                style={{ width: 120 }}\n                allowClear\n              >\n                <Select.Option value={InvitationStatus.PENDING}>待确认</Select.Option>\n                <Select.Option value={InvitationStatus.ACCEPTED}>已接受</Select.Option>\n                <Select.Option value={InvitationStatus.REJECTED}>已拒绝</Select.Option>\n                <Select.Option value={InvitationStatus.EXPIRED}>已过期</Select.Option>\n                <Select.Option value={InvitationStatus.CANCELLED}>已取消</Select.Option>\n              </Select>\n            </Space>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchInvitations}\n              loading={invitationLoading}\n            >\n              刷新\n            </Button>\n          </Space>\n        </div>\n\n        {/* 邀请列表 */}\n        <Table\n          columns={invitationColumns}\n          dataSource={filteredInvitations}\n          rowKey=\"id\"\n          loading={invitationLoading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条邀请记录`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n            extra=\"您可以添加一些邀请消息，让被邀请人更好地了解邀请意图\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"欢迎加入我们的团队！我们期待与您一起工作...\"\n              maxLength={500}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCylBb;;;2BAAA;;;;;;;oFApkB2C;yCAepC;0CASA;mFAEW;yCAGU;+CACM;wCAED;8FACK;;;;;;;;;;YAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;YAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;gBACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;gBAEjC,WAAW;gBACX,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;gBAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;gBAC3D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAS;gBAEzD,IAAA,gBAAS,EAAC;oBACR;oBACA;gBACF,GAAG,EAAE;gBAEL,MAAM,eAAe;oBACnB,IAAI;wBACF,WAAW;wBACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;wBAC1D,WAAW,cAAc,EAAE;oBAC7B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,WAAW,EAAE,GAAG,eAAe;oBACjC,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,SAAS;gBACT,MAAM,mBAAmB;oBACvB,IAAI;wBACF,qBAAqB;wBACrB,MAAM,iBAAiB,MAAM,6BAAiB,CAAC,yBAAyB;wBACxE,eAAe,kBAAkB,EAAE;oBACrC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,eAAe,EAAE;oBACnB,SAAU;wBACR,qBAAqB;oBACvB;gBACF;gBAEA,QAAQ;gBACR,MAAM,sBAAsB,OAAO;oBACjC,IAAI;wBACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;wBAEnB,aAAa;wBACb,MAAM,WAAW,MAAM,6BAAiB,CAAC,eAAe,CAAC;4BACvD,QAAQ;4BACR,SAAS,OAAO,OAAO;wBACzB;wBAEA,8EAA8E;wBAC9E,2BAA2B;wBAC3B,IAAI,SAAS,YAAY,GAAG,GAAG;4BAC7B,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,YAAY,CAAC,IAAI,CAAC;4BAIjD,QAAQ,GAAG,CAAC,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,CAAA,MAAQ,CAAA;oCACpD,OAAO,IAAI,KAAK;oCAChB,MAAM,IAAI,cAAc;gCAC1B,CAAA;wBAEJ;wBAEA,IAAI,SAAS,YAAY,GAAG,GAC1B,aAAO,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,QAAQ,CAAC;wBAGpD,sBAAsB;wBACtB,WAAW,WAAW;wBACtB;wBACA,oBAAoB,SAAS;wBAC7B,aAAa,SAAS;oBACxB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,OAAO;gBACP,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,6BAAiB,CAAC,gBAAgB,CAAC;wBACzC,aAAO,CAAC,OAAO,CAAC;wBAChB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,qBAAqB,OAAO;oBAChC,IAAI;wBACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;wBACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;wBACtC;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,oBAAoB;oBACxB,IAAI;wBACF,MAAM,YAAY;wBAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;wBAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;wBAC7C,mBAAmB,EAAE;wBACrB;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,OAAO;gBACP,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAG5D,UAAU;gBACV,MAAM,2BAA2B,OAAO,QAA4B;oBAClE,IAAI;wBACF,MAAM,iBAAW,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;wBAChD,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;wBAC7D;wBACA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA,QAAQ;gBACR,MAAM,UAA2C;oBAC/C;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAc,uBACrB,2BAAC,WAAK;;kDACJ,2BAAC;wCAAK,MAAM;kDAAE;;;;;;oCACb,OAAO,SAAS,kBACf,2BAAC,SAAG;wCAAC,oBAAM,2BAAC,oBAAa;;;;;wCAAK,OAAM;kDAAO;;;;;;;;;;;;oBAInD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,sBACP,2BAAC;gCAAK,MAAK;0CAAa;;;;;;oBAE5B;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;gCAAC,OAAO,WAAW,UAAU;0CAC9B,WAAW,OAAO;;;;;;oBAGzB;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;oBAC7D;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,OAAO;wBACP,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,SAAS,EAClB,qBAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;4BAGhC,qBACE,2BAAC,WAAK;;oCACH,OAAO,QAAQ,iBACd,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;6DAID,2BAAC,YAAM;wCACL,MAAK;wCACL,MAAK;wCACL,SAAS,IAAM,yBAAyB,QAAQ;kDACjD;;;;;;kDAIH,2BAAC,gBAAU;wCACT,OAAM;wCACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;wCAChD,WAAW,IAAM,mBAAmB;wCACpC,QAAO;wCACP,YAAW;wCACX,QAAO;kDAEP,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,MAAM;4CACN,MAAK;4CACL,oBAAM,2BAAC,qBAAc;;;;;sDACtB;;;;;;;;;;;;;;;;;wBAMT;oBACF;iBACD;gBAED,QAAQ;gBACR,MAAM,eAAe;oBACnB;oBACA,UAAU;oBACV,kBAAkB,CAAC,SAAgC,CAAA;4BACjD,UAAU,OAAO,SAAS;wBAC5B,CAAA;gBACF;gBAEA,UAAU;gBACV,MAAM,oBAAyD;oBAC7D;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAM;;kDAChC,2BAAC;wCAAK,MAAM;kDAAE,OAAO,WAAW,IAAI;;;;;;kDACpC,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAO;;0DAC/C,2BAAC,mBAAY;;;;;4CAAG;4CAAE,OAAO,YAAY;;;;;;;;;;;;;oBAI7C;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,QAAQ,uBACf,2BAAC,yBAAyB;gCACxB,QAAQ;gCACR,WAAW,OAAO,SAAS;;;;;;wBAG/B,SAAS;4BACP;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,QAAQ;4BAAC;4BAChD;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,OAAO;4BAAC;4BAC/C;gCAAE,MAAM;gCAAO,OAAO,qBAAgB,CAAC,SAAS;4BAAC;yBAClD;wBACD,UAAU,CAAC,OAAO,SAAW,OAAO,MAAM,KAAK;oBACjD;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,qBACP,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;wBAGxB,QAAQ,CAAC,GAAG,IAAM,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI,KAAK,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI;oBACvE;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,KAAK;wBACL,QAAQ,CAAC,MAAM;4BACb,MAAM,YAAY,OAAO,SAAS;4BAClC,qBACE,2BAAC,aAAO;gCAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;0CACjC,cAAA,2BAAC;oCAAK,MAAM,YAAY,WAAW;8CAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;;;;;;wBAI5B;oBACF;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,QAAQ,CAAC,GAAG;4BACV,IAAI,OAAO,MAAM,KAAK,qBAAgB,CAAC,OAAO,IAAI,CAAC,OAAO,SAAS,EACjE,qBACE,2BAAC,gBAAU;gCACT,OAAM;gCACN,WAAW,IAAM,uBAAuB,OAAO,EAAE;gCACjD,QAAO;gCACP,YAAW;0CAEX,cAAA,2BAAC,YAAM;oCAAC,MAAK;oCAAQ,MAAM;oCAAC,oBAAM,2BAAC,qBAAc;;;;;8CAAK;;;;;;;;;;;4BAM5D,qBAAO,2BAAC;gCAAK,MAAK;0CAAY;;;;;;wBAChC;oBACF;iBACD;gBAED,SAAS;gBACT,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;oBAC7C,MAAM,gBAAgB,CAAC,wBACrB,WAAW,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW,OAC9E,WAAW,WAAW,IAAI,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB,WAAW;oBAE3G,MAAM,gBAAgB,CAAC,gBAAgB,WAAW,MAAM,KAAK;oBAE7D,OAAO,iBAAiB;gBAC1B;gBAEA,qBACE,2BAAC;;sCAEC,2BAAC,UAAI;4BACH,qBACE,2BAAC,WAAK;;kDACJ,2BAAC,mBAAY;;;;;kDACb,2BAAC;;4CAAK;4CAAQ,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;4CAAC;;;;;;;;;;;;;4BAGxC,OAAO;gCAAE,cAAc;4BAAG;;8CAG1B,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,cAAA,2BAAC,WAAK;wCAAC,OAAO;4CAAE,OAAO;4CAAQ,gBAAgB;wCAAgB;;0DAC7D,2BAAC,WAAK;;kEACJ,2BAAC,WAAK;wDACJ,aAAY;wDACZ,sBAAQ,2BAAC,qBAAc;;;;;wDACvB,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,OAAO;4DAAE,OAAO;wDAAI;;;;;;oDAErB,gBAAgB,MAAM,GAAG,mBACxB,2BAAC,gBAAU;wDACT,OAAO,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;wDACjD,WAAW;wDACX,QAAO;wDACP,YAAW;kEAEX,cAAA,2BAAC,YAAM;4DAAC,MAAM;4DAAC,oBAAM,2BAAC,qBAAc;;;;;;gEAAK;gEAChC,gBAAgB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;0DAKtC,2BAAC,YAAM;gDACL,MAAK;gDACL,oBAAM,2BAAC,sBAAe;;;;;gDACtB,SAAS,IAAM,sBAAsB;0DACtC;;;;;;;;;;;;;;;;;8CAOL,2BAAC,WAAK;oCACJ,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,cAAc;oCACd,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;wCACtC,UAAU;oCACZ;;;;;;;;;;;;sCAKJ,2BAAC,UAAI;4BACH,qBACE,2BAAC,WAAK;;kDACJ,2BAAC,mBAAY;;;;;kDACb,2BAAC;;4CAAK;4CAAQ,CAAA,eAAe,EAAE,AAAD,EAAG,MAAM;4CAAC;;;;;;;;;;;;;;8CAK5C,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,cAAA,2BAAC,WAAK;wCAAC,OAAO;4CAAE,OAAO;4CAAQ,gBAAgB;wCAAgB;;0DAC7D,2BAAC,WAAK;;kEACJ,2BAAC,WAAK;wDACJ,aAAY;wDACZ,sBAAQ,2BAAC,qBAAc;;;;;wDACvB,OAAO;wDACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;wDACvD,OAAO;4DAAE,OAAO;wDAAI;;;;;;kEAEtB,2BAAC,YAAM;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU;wDACV,OAAO;4DAAE,OAAO;wDAAI;wDACpB,UAAU;;0EAEV,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,OAAO;0EAAE;;;;;;0EAChD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,QAAQ;0EAAE;;;;;;0EACjD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,QAAQ;0EAAE;;;;;;0EACjD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,OAAO;0EAAE;;;;;;0EAChD,2BAAC,YAAM,CAAC,MAAM;gEAAC,OAAO,qBAAgB,CAAC,SAAS;0EAAE;;;;;;;;;;;;;;;;;;0DAGtD,2BAAC,YAAM;gDACL,oBAAM,2BAAC,qBAAc;;;;;gDACrB,SAAS;gDACT,SAAS;0DACV;;;;;;;;;;;;;;;;;8CAOL,2BAAC,WAAK;oCACJ,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;wCACxC,UAAU;oCACZ;;;;;;;;;;;;sCAOJ,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,sBAAsB;gCACtB,WAAW,WAAW;4BACxB;4BACA,QAAQ;4BACR,OAAO;sCAEP,cAAA,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,UAAU;;kDAEV,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCACtC;wCACD,OAAM;kDAEN,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;;;;;;;;;;;kDAGhB,2BAAC,UAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAM;kDAEN,cAAA,2BAAC;4CACC,MAAM;4CACN,aAAY;4CACZ,WAAW;4CACX,SAAS;;;;;;;;;;;kDAGb,2BAAC,UAAI,CAAC,IAAI;kDACR,cAAA,2BAAC,WAAK;;8DACJ,2BAAC,YAAM;oDAAC,MAAK;oDAAU,UAAS;oDAAS,oBAAM,2BAAC,mBAAY;;;;;8DAAK;;;;;;8DAGjE,2BAAC,YAAM;oDAAC,SAAS,IAAM,sBAAsB;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASnE;eAvhBM;;oBASiB,UAAI,CAAC;;;iBATtB;gBAyhBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDzlBD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}