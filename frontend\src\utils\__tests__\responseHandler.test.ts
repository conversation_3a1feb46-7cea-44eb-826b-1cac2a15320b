/**
 * ResponseHandler 测试文件
 */

import { ResponseHandler, responseHandlers } from '../responseHandler';
import type { ApiResponse } from '@/types/api';

// Mock antd message
const mockSuccess = jest.fn();
const mockError = jest.fn();

jest.mock('antd', () => ({
  message: {
    success: mockSuccess,
    error: mockError,
  },
}));

describe('ResponseHandler', () => {
  beforeEach(() => {
    mockSuccess.mockClear();
    mockError.mockClear();
  });

  describe('handle', () => {
    it('should return data when code is 200', () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = ResponseHandler.handle(response);
      expect(result).toBe('test data');
      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockError).not.toHaveBeenCalled();
    });

    it('should show success message when configured', () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = ResponseHandler.handle(response, {
        showSuccessMessage: true,
      });

      expect(result).toBe('test data');
      expect(mockSuccess).toHaveBeenCalledWith('Success');
    });

    it('should show custom success message', () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = ResponseHandler.handle(response, {
        showSuccessMessage: true,
        successMessage: 'Custom success',
      });

      expect(result).toBe('test data');
      expect(mockSuccess).toHaveBeenCalledWith('Custom success');
    });

    it('should show error message when code is not 200', () => {
      const response: ApiResponse<string> = {
        code: 400,
        message: 'Bad request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      expect(() => ResponseHandler.handle(response)).toThrow('Bad request');
      expect(mockError).toHaveBeenCalledWith('Bad request');
    });

    it('should use custom error handler', () => {
      const customErrorHandler = jest.fn();
      const response: ApiResponse<string> = {
        code: 400,
        message: 'Bad request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      expect(() => ResponseHandler.handle(response, {
        customErrorHandler,
      })).toThrow('Bad request');

      expect(customErrorHandler).toHaveBeenCalledWith(400, 'Bad request');
      expect(mockError).not.toHaveBeenCalled();
    });

    it('should not show messages in silent mode', () => {
      const response: ApiResponse<string> = {
        code: 400,
        message: 'Bad request',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      expect(() => ResponseHandler.handle(response, {
        silent: true,
      })).toThrow('Bad request');

      expect(mockMessage.error).not.toHaveBeenCalled();
    });
  });

  describe('handleAsync', () => {
    it('should handle successful promise', async () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = await ResponseHandler.handleAsync(Promise.resolve(response));
      expect(result).toBe('test data');
    });

    it('should handle rejected promise', async () => {
      const error = new Error('Network error');
      
      await expect(ResponseHandler.handleAsync(Promise.reject(error)))
        .rejects.toThrow('Network error');
      
      expect(mockMessage.error).toHaveBeenCalledWith('Network error');
    });

    it('should handle rejected promise in silent mode', async () => {
      const error = new Error('Network error');
      
      await expect(ResponseHandler.handleAsync(Promise.reject(error), {
        silent: true,
      })).rejects.toThrow('Network error');
      
      expect(mockMessage.error).not.toHaveBeenCalled();
    });
  });

  describe('handleBatch', () => {
    it('should handle multiple successful responses', () => {
      const responses: ApiResponse<string>[] = [
        {
          code: 200,
          message: 'Success 1',
          data: 'data 1',
          timestamp: '2023-01-01T00:00:00Z',
        },
        {
          code: 200,
          message: 'Success 2',
          data: 'data 2',
          timestamp: '2023-01-01T00:00:00Z',
        },
      ];

      const results = ResponseHandler.handleBatch(responses);
      expect(results).toEqual(['data 1', 'data 2']);
      expect(mockMessage.error).not.toHaveBeenCalled();
    });

    it('should handle mixed responses', () => {
      const responses: ApiResponse<string>[] = [
        {
          code: 200,
          message: 'Success',
          data: 'data 1',
          timestamp: '2023-01-01T00:00:00Z',
        },
        {
          code: 400,
          message: 'Error',
          data: null,
          timestamp: '2023-01-01T00:00:00Z',
        },
      ];

      const results = ResponseHandler.handleBatch(responses);
      expect(results).toEqual(['data 1']);
      expect(mockMessage.error).toHaveBeenCalledWith('第2个请求失败: Error');
    });
  });

  describe('predefined handlers', () => {
    it('should use default handler', () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = responseHandlers.default(response);
      expect(result).toBe('test data');
    });

    it('should use silent handler', () => {
      const response: ApiResponse<string> = {
        code: 400,
        message: 'Error',
        data: null,
        timestamp: '2023-01-01T00:00:00Z',
      };

      expect(() => responseHandlers.silent(response)).toThrow('Error');
      expect(mockMessage.error).not.toHaveBeenCalled();
    });

    it('should use verbose handler', () => {
      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = responseHandlers.verbose(response);
      expect(result).toBe('test data');
      expect(mockMessage.success).toHaveBeenCalledWith('Success');
    });
  });

  describe('createHandler', () => {
    it('should create handler with default config', () => {
      const handler = ResponseHandler.createHandler({
        showSuccessMessage: true,
        successMessage: 'Default success',
      });

      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = handler(response);
      expect(result).toBe('test data');
      expect(mockMessage.success).toHaveBeenCalledWith('Default success');
    });

    it('should override default config', () => {
      const handler = ResponseHandler.createHandler({
        showSuccessMessage: true,
        successMessage: 'Default success',
      });

      const response: ApiResponse<string> = {
        code: 200,
        message: 'Success',
        data: 'test data',
        timestamp: '2023-01-01T00:00:00Z',
      };

      const result = handler(response, {
        successMessage: 'Override success',
      });

      expect(result).toBe('test data');
      expect(mockMessage.success).toHaveBeenCalledWith('Override success');
    });
  });
});
