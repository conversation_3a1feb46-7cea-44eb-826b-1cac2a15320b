globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/team.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TeamService: function() {
                    return TeamService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
            var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class TeamService {
                /**
   * 创建团队
   *
   * 创建新的团队，创建者自动成为团队管理员。
   * 需要用户级别的Token（Account Token）。
   *
   * @param data 团队创建请求参数
   * @param data.name 团队名称（必填，2-50字符）
   * @param data.description 团队描述（可选）
   * @returns Promise<TeamDetailResponse> 创建的团队信息
   * @throws 当团队名称重复或用户权限不足时抛出异常
   *
   * @example
   * ```typescript
   * const newTeam = await TeamService.createTeam({
   *   name: '开发团队',
   *   description: '负责产品开发的团队'
   * });
   * console.log('团队创建成功:', newTeam.name);
   * ```
   */ static async createTeam(data) {
                    const response = await _request.apiRequest.post('/teams', data);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 获取用户的团队列表
   *
   * 获取当前用户所属的所有团队的基本信息。
   * 需要用户级别的Token（Account Token）。
   *
   * @returns Promise<TeamDetailResponse[]> 团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeams();
   * console.log('用户所属团队数量:', teams.length);
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, ID: ${team.id}`);
   * });
   * ```
   */ static async getUserTeams() {
                    const response = await _request.apiRequest.get('/teams');
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 获取用户的团队列表（包含统计数据）
   *
   * 获取当前用户所属的所有团队，包含每个团队的统计信息。
   * 用于个人中心的团队列表展示。
   *
   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const teams = await TeamService.getUserTeamsWithStats();
   * teams.forEach(team => {
   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);
   * });
   * ```
   */ static async getUserTeamsWithStats() {
                    const response = await _request.apiRequest.get('/teams?includeStats=true');
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 获取当前团队详情
   *
   * 获取当前选择团队的详细信息。
   * 需要团队级别的Token（Team Token）。
   *
   * @returns Promise<TeamDetailResponse> 团队详细信息
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamDetail = await TeamService.getCurrentTeamDetail();
   * console.log('当前团队:', teamDetail.name);
   * console.log('团队描述:', teamDetail.description);
   * console.log('成员数量:', teamDetail.memberCount);
   * ```
   */ static async getCurrentTeamDetail() {
                    const response = await _request.apiRequest.get('/teams/current');
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 更新当前团队信息
   *
   * 更新当前团队的基本信息，如名称、描述等。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 团队更新请求参数
   * @param data.name 新的团队名称（可选）
   * @param data.description 新的团队描述（可选）
   * @returns Promise<TeamDetailResponse> 更新后的团队信息
   * @throws 当用户非团队创建者或团队名称重复时抛出异常
   *
   * @example
   * ```typescript
   * const updatedTeam = await TeamService.updateCurrentTeam({
   *   name: '新团队名称',
   *   description: '更新后的团队描述'
   * });
   * console.log('团队信息更新成功');
   * ```
   */ static async updateCurrentTeam(data) {
                    const response = await _request.apiRequest.put('/teams/current', data);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 删除当前团队（需要 Team Token，仅创建者）
   *
   * 权限要求：
   * - 需要有效的Team Token
   * - 只有团队创建者可以执行此操作
   *
   * 删除效果：
   * - 软删除团队记录
   * - 级联删除所有团队成员关系
   * - 不可恢复
   *
   * @returns Promise<void> 删除成功时resolve
   * @throws 当权限不足或团队不存在时抛出异常
   */ static async deleteCurrentTeam() {
                    const response = await _request.apiRequest.delete('/teams/current');
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                }
                /**
   * 获取当前团队成员列表（简单数组格式）
   *
   * 获取当前团队的所有成员，返回简单数组格式。
   * 内部调用分页接口并获取所有成员。
   *
   * @returns Promise<TeamMemberResponse[]> 团队成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const members = await TeamService.getCurrentTeamMembers();
   * console.log('团队成员数量:', members.length);
   * members.forEach(member => {
   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);
   * });
   * ```
   */ static async getCurrentTeamMembers() {
                    const response = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    return (response === null || response === void 0 ? void 0 : response.list) || [];
                }
                /**
   * 获取当前团队成员列表（分页格式）
   *
   * 获取当前团队的成员列表，支持分页查询。
   * 需要团队级别的Token（Team Token）。
   *
   * @param params 分页查询参数（可选）
   * @param params.current 当前页码（默认1）
   * @param params.pageSize 每页大小（默认10）
   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const membersPage = await TeamService.getTeamMembers({
   *   current: 1,
   *   pageSize: 20
   * });
   *
   * console.log('总成员数:', membersPage.total);
   * console.log('当前页成员:', membersPage.list);
   * ```
   */ static async getTeamMembers(params) {
                    const response = await _request.apiRequest.get('/teams/current/members', params);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 邀请团队成员
   *
   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param data 邀请请求参数
   * @param data.emails 被邀请人的邮箱列表
   * @returns Promise<void> 邀请发送成功时resolve
   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.inviteMembers({
   *   emails: ['<EMAIL>', '<EMAIL>']
   * });
   * console.log('邀请已发送');
   * ```
   */ static async inviteMembers(data) {
                    const response = await _request.apiRequest.post('/teams/current/members/invite', data);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 移除团队成员
   *
   * 从当前团队中移除指定成员。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 要移除的成员ID
   * @returns Promise<void> 移除成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * await TeamService.removeMember(123);
   * console.log('成员已移除');
   * ```
   */ static async removeMember(memberId) {
                    const response = await _request.apiRequest.delete(`/teams/current/members/${memberId}`);
                    // Check response code and handle errors
                    if (!_responseCodes.ResponseCode.isSuccess(response.code)) {
                        (0, _errorHandler.handleApiError)(response);
                        throw new Error(response.message);
                    }
                    return response.data;
                }
                /**
   * 更新团队成员状态
   *
   * 更新团队成员的激活状态（启用/禁用）。
   * 需要团队级别的Token，且仅团队创建者有权限。
   *
   * @param memberId 成员ID
   * @param isActive 是否激活（true=启用，false=禁用）
   * @returns Promise<void> 更新成功时resolve
   * @throws 当用户非团队创建者或成员不存在时抛出异常
   *
   * @example
   * ```typescript
   * // 禁用成员
   * await TeamService.updateMemberStatus(123, false);
   * console.log('成员已禁用');
   *
   * // 启用成员
   * await TeamService.updateMemberStatus(123, true);
   * console.log('成员已启用');
   * ```
   */ static async updateMemberStatus(memberId, isActive) {
                    const response = await _request.apiRequest.put(`/teams/current/members/${memberId}/status?isActive=${isActive}`);
                    return response.data;
                }
                /**
   * 获取团队统计信息
   *
   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。
   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。
   *
   * @returns Promise<object> 团队统计信息
   * @returns Promise<object>.memberCount 总成员数
   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）
   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）
   * @throws 当未选择团队或无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const stats = await TeamService.getTeamStats();
   * console.log('总成员数:', stats.memberCount);
   * console.log('活跃成员数:', stats.activeMembers);
   * console.log('最近活跃成员数:', stats.recentActivity);
   * ```
   */ static async getTeamStats() {
                    // 这里可能需要后端提供专门的统计接口
                    // 暂时通过团队详情和成员列表来计算
                    const teamDetail = await TeamService.getCurrentTeamDetail();
                    const members = await TeamService.getTeamMembers({
                        current: 1,
                        pageSize: 1000
                    });
                    const activeMembers = members.list.filter((member)=>member.isActive).length;
                    const recentActivity = members.list.filter((member)=>{
                        const lastAccess = new Date(member.lastAccessTime);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return lastAccess > weekAgo;
                    }).length;
                    return {
                        memberCount: teamDetail.memberCount,
                        activeMembers,
                        recentActivity
                    };
                }
            }
            var _default = TeamService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17899743907717216225';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.10970044588545715927.hot-update.js.map