globalThis.makoModuleHotUpdate('p__team__index', {
    modules: {
        "src/pages/team/components/TeamListContent.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var _teamSelectionUtils = __mako_require__("src/utils/teamSelectionUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const { Search } = _antd.Input;
            const TeamListContent = ()=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [teams, setTeams] = (0, _react.useState)([]);
                const [filteredTeams, setFilteredTeams] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [currentTeamId, setCurrentTeamId] = (0, _react.useState)(null);
                (0, _react.useEffect)(()=>{
                    fetchTeams();
                    // 获取当前团队ID
                    const teamId = (0, _tokenUtils.getTeamIdFromCurrentToken)();
                    setCurrentTeamId(teamId);
                }, []);
                (0, _react.useEffect)(()=>{
                    // 过滤团队列表
                    const filtered = teams.filter((team)=>team.name.toLowerCase().includes(searchText.toLowerCase()) || team.description && team.description.toLowerCase().includes(searchText.toLowerCase()));
                    setFilteredTeams(filtered);
                }, [
                    teams,
                    searchText
                ]);
                const fetchTeams = async ()=>{
                    try {
                        setLoading(true);
                        const teamList = await _services.TeamService.getUserTeams();
                        setTeams(teamList);
                    } catch (error) {
                        console.error('获取团队列表失败:', error);
                        _antd.message.error('获取团队列表失败');
                    } finally{
                        setLoading(false);
                    }
                };
                const handleCreateTeam = ()=>{
                    _max.history.push('/personal-center');
                };
                const handleViewTeam = async (team)=>{
                    try {
                        // 切换到该团队
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                            _antd.message.success(`已切换到团队：${team.name}`);
                            // 更新当前团队ID
                            setCurrentTeamId(team.id);
                        // 不需要刷新页面，让应用自然响应状态变化
                        } else {
                            console.debug('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        // Error message already displayed by service layer
                        console.debug('切换团队失败:', error);
                    }
                };
                const handleSwitchTeam = async (team)=>{
                    try {
                        const response = await _services.AuthService.teamLogin({
                            teamId: team.id
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === team.id) {
                            _antd.message.success(`已切换到团队：${team.name}`);
                            // 记录用户选择了这个团队
                            const currentUserId = (0, _tokenUtils.getUserIdFromCurrentToken)();
                            if (currentUserId) (0, _teamSelectionUtils.recordTeamSelection)(currentUserId, team.id);
                            // 更新当前团队ID
                            setCurrentTeamId(team.id);
                            // 等待一段时间确保 Token 更新完成后再跳转
                            setTimeout(()=>{
                                _max.history.push('/');
                            }, 200);
                        } else {
                            console.error('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        console.error('切换团队失败:', error);
                        // 确保错误消息能够显示给用户
                        if (error.message) _antd.message.error(error.message);
                        else _antd.message.error('团队切换失败，请重试');
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Search, {
                                placeholder: "搜索团队名称或描述",
                                allowClear: true,
                                value: searchText,
                                onChange: (e)=>setSearchText(e.target.value),
                                style: {
                                    width: 300
                                }
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 146,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 145,
                            columnNumber: 7
                        }, this),
                        filteredTeams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                            image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                            description: searchText ? '没有找到匹配的团队' : '您还没有加入任何团队',
                            children: !searchText && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                onClick: handleCreateTeam,
                                children: "创建第一个团队"
                            }, void 0, false, {
                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                lineNumber: 163,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 156,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                            loading: loading,
                            itemLayout: "horizontal",
                            dataSource: filteredTeams,
                            pagination: {
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total)=>`共 ${total} 个团队`
                            },
                            renderItem: (team)=>{
                                const isCurrentTeam = currentTeamId === team.id;
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                    style: {
                                        backgroundColor: isCurrentTeam ? '#f6ffed' : undefined,
                                        border: isCurrentTeam ? '1px solid #b7eb8f' : undefined,
                                        borderRadius: isCurrentTeam ? '8px' : undefined,
                                        padding: isCurrentTeam ? '16px' : undefined
                                    },
                                    actions: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EyeOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 192,
                                                columnNumber: 27
                                            }, void 0),
                                            onClick: ()=>handleViewTeam(team),
                                            children: "查看详情"
                                        }, "view", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, void 0),
                                        isCurrentTeam ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                            color: "green",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 201,
                                                columnNumber: 29
                                            }, void 0),
                                            children: "当前团队"
                                        }, "current", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 198,
                                            columnNumber: 21
                                        }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            size: "small",
                                            onClick: ()=>handleSwitchTeam(team),
                                            children: "进入团队"
                                        }, "switch", false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 206,
                                            columnNumber: 21
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item.Meta, {
                                        avatar: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 64,
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team/components/TeamListContent.tsx",
                                                lineNumber: 221,
                                                columnNumber: 29
                                            }, void 0),
                                            style: {
                                                backgroundColor: isCurrentTeam ? '#52c41a' : '#1890ff',
                                                border: isCurrentTeam ? '2px solid #389e0d' : undefined
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 219,
                                            columnNumber: 21
                                        }, void 0),
                                        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 4,
                                                    style: {
                                                        margin: 0
                                                    },
                                                    children: team.name
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 230,
                                                    columnNumber: 23
                                                }, void 0),
                                                isCurrentTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "green",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 234,
                                                        columnNumber: 50
                                                    }, void 0),
                                                    children: "当前团队"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 25
                                                }, void 0),
                                                team.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                    color: "gold",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                                        lineNumber: 239,
                                                        columnNumber: 49
                                                    }, void 0),
                                                    children: "创建者"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 25
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 229,
                                            columnNumber: 21
                                        }, void 0),
                                        description: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: "small",
                                            children: [
                                                team.description && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    type: "secondary",
                                                    children: team.description
                                                }, void 0, false, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 248,
                                                    columnNumber: 25
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: "small",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 252,
                                                                    columnNumber: 27
                                                                }, void 0),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    type: "secondary",
                                                                    children: [
                                                                        team.memberCount,
                                                                        " 名成员"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                                    lineNumber: 253,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 251,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        team.assignedAt && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "加入于 ",
                                                                new Date(team.assignedAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 256,
                                                            columnNumber: 27
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "创建于 ",
                                                                new Date(team.createdAt).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 260,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 23
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: "状态："
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 265,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                            color: team.isActive ? 'green' : 'red',
                                                            children: team.isActive ? '启用' : '停用'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 266,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        team.lastAccessTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            children: [
                                                                "最后访问：",
                                                                new Date(team.lastAccessTime).toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                                            lineNumber: 270,
                                                            columnNumber: 27
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team/components/TeamListContent.tsx",
                                            lineNumber: 246,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/team/components/TeamListContent.tsx",
                                        lineNumber: 217,
                                        columnNumber: 17
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/team/components/TeamListContent.tsx",
                                    lineNumber: 181,
                                    columnNumber: 15
                                }, void 0);
                            }
                        }, void 0, false, {
                            fileName: "src/pages/team/components/TeamListContent.tsx",
                            lineNumber: 169,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team/components/TeamListContent.tsx",
                    lineNumber: 144,
                    columnNumber: 5
                }, this);
            };
            _s(TeamListContent, "oikW+lvAhQh6tcKAb30N1MiMeXY=");
            _c = TeamListContent;
            var _default = TeamListContent;
            var _c;
            $RefreshReg$(_c, "TeamListContent");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '8779625074155543297';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=p__team__index-async.4264192332565391698.hot-update.js.map