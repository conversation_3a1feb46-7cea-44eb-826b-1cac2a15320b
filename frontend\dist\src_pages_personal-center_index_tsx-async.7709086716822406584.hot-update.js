globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/TeamListCard.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _services = __mako_require__("src/services/index.ts");
            var _team = __mako_require__("src/services/team.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var _teamSelectionUtils = __mako_require__("src/utils/teamSelectionUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text, Title } = _antd.Typography;
            // 响应式布局样式
            const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 8px;
    }

    .team-stats-row {
      margin-top: 8px;
    }

    .team-info-wrap {
      gap: 8px !important;
    }
  }

  @media (max-width: 576px) {
    .team-stats-row {
      margin-top: 12px;
    }

    .team-stats-col {
      margin-bottom: 4px;
    }

    .team-info-wrap {
      gap: 6px !important;
    }

    .team-meta-info {
      flex-wrap: wrap;
      gap: 6px !important;
    }

    .team-status-badges {
      flex-wrap: wrap;
      gap: 4px !important;
      margin-top: 4px;
    }
  }

  @media (max-width: 480px) {
    .team-name-text {
      font-size: 14px !important;
    }

    .team-meta-text {
      font-size: 11px !important;
    }

    .team-meta-info {
      gap: 4px !important;
    }

    .team-status-badges {
      gap: 3px !important;
    }
  }
`;
            const TeamListCard = ()=>{
                _s();
                // 团队列表状态管理
                const [teams, setTeams] = (0, _react.useState)([]);
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
                const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
                const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
                // 获取当前Token中的团队信息和用户信息
                const currentTokenTeamId = (0, _tokenUtils.getTeamIdFromCurrentToken)();
                const currentUserId = (0, _tokenUtils.getUserIdFromCurrentToken)();
                const hasTeamInToken = (0, _tokenUtils.hasTeamInCurrentToken)();
                // 判断是否有真正的当前团队：
                // 1. Token中有团队信息（说明用户已经选择过团队）
                // 2. initialState中有团队信息（说明已经获取过团队详情）
                // 3. 两者的团队ID一致（确保状态同步）
                // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）
                const hasRealCurrentTeam = !!(hasTeamInToken && currentTokenTeamId && currentTeam && currentTeam.id === currentTokenTeamId && currentUserId && (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId));
                // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID
                const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;
                // 调试日志
                console.log('TeamListCard 状态调试:', {
                    currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
                    currentTokenTeamId,
                    currentUserId,
                    hasTeamInToken,
                    hasRealCurrentTeam,
                    actualCurrentTeamId,
                    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId) : false,
                    initialStateCurrentUser: !!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)
                });
                // 获取团队列表数据
                (0, _react.useEffect)(()=>{
                    const fetchTeams = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            const teamsData = await _team.TeamService.getUserTeamsWithStats();
                            setTeams(teamsData);
                        } catch (error) {
                            // Error message already displayed by service layer
                            console.debug('获取团队列表失败:', error);
                            setError('获取团队列表失败');
                        } finally{
                            setLoading(false);
                        }
                    };
                    // 只有在用户已登录时才获取团队列表
                    if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) fetchTeams();
                }, [
                    initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
                ]);
                // 监听全局状态变化，处理注销等情况
                (0, _react.useEffect)(()=>{
                    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态
                    if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) {
                        setTeams([]);
                        setError(null);
                        setLoading(false);
                        setSwitchingTeamId(null);
                    }
                }, [
                    initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
                ]);
                // 监听当前团队状态变化
                (0, _react.useEffect)(()=>{
                    console.log('当前团队状态变化:', {
                        currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
                        actualCurrentTeamId,
                        hasRealCurrentTeam
                    });
                }, [
                    currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
                    actualCurrentTeamId,
                    hasRealCurrentTeam
                ]);
                // 团队切换处理函数
                const handleTeamSwitch = async (teamId, teamName)=>{
                    // 检查用户是否已登录
                    if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) {
                        _antd.message.error('请先登录');
                        return;
                    }
                    try {
                        setSwitchingTeamId(teamId);
                        // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API
                        if (teamId === actualCurrentTeamId) {
                            _antd.message.success(`进入团队：${teamName}`);
                            _max.history.push('/dashboard');
                            return;
                        }
                        // 非当前团队，执行切换逻辑
                        const response = await _services.AuthService.selectTeam({
                            teamId
                        });
                        // 检查后端返回的团队选择成功标识
                        if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                            _antd.message.success(`已切换到团队：${teamName}`);
                            // 记录用户选择了这个团队
                            if (currentUserId) (0, _teamSelectionUtils.recordTeamSelection)(currentUserId, teamId);
                            // 由于Token已经更新，路由守卫现在能够正确识别团队信息，可以直接跳转
                            // 同时异步更新 initialState 以保持状态同步
                            if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) // 异步更新状态，不阻塞跳转
                            Promise.all([
                                initialState.fetchUserInfo(),
                                initialState.fetchTeamInfo()
                            ]).then(([currentUser, currentTeam])=>{
                                if (currentTeam && currentTeam.id === teamId) setInitialState({
                                    ...initialState,
                                    currentUser,
                                    currentTeam
                                });
                            }).catch((error)=>{
                                console.error('更新 initialState 失败:', error);
                            });
                            // 直接跳转，路由守卫会处理团队验证
                            _max.history.push('/dashboard');
                        } else {
                            console.debug('团队切换响应异常，未返回正确的团队信息');
                            _antd.message.error('团队切换失败，请重试');
                        }
                    } catch (error) {
                        // Error message already displayed by service layer
                        // Only log for debugging purposes, don't show console.error to user
                        console.debug('团队切换失败:', error);
                    } finally{
                        setSwitchingTeamId(null);
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("style", {
                            dangerouslySetInnerHTML: {
                                __html: styles
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 279,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            className: "dashboard-card",
                            style: {
                                borderRadius: 16,
                                boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
                                border: 'none',
                                background: 'linear-gradient(145deg, #ffffff, #f8faff)'
                            },
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 4,
                                style: {
                                    margin: 0,
                                    background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    fontWeight: 600
                                },
                                children: "团队列表"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 290,
                                columnNumber: 11
                            }, void 0),
                            children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                message: "团队列表加载失败",
                                description: error,
                                type: "error",
                                showIcon: true,
                                style: {
                                    marginBottom: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 305,
                                columnNumber: 11
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                spinning: loading,
                                children: !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center',
                                        padding: '40px 20px'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "请先登录以查看团队列表"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                        lineNumber: 316,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 315,
                                    columnNumber: 15
                                }, this) : teams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center',
                                        padding: '40px 20px'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "暂无团队，请在设置中创建团队或等待邀请加入团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                        lineNumber: 320,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 319,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    dataSource: teams,
                                    renderItem: (item)=>{
                                        var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                className: "team-item",
                                                style: {
                                                    background: actualCurrentTeamId === item.id ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)' : '#fff',
                                                    borderRadius: 8,
                                                    boxShadow: actualCurrentTeamId === item.id ? '0 2px 8px rgba(24, 144, 255, 0.12)' : '0 1px 4px rgba(0,0,0,0.06)',
                                                    width: '100%',
                                                    borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,
                                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                    border: actualCurrentTeamId === item.id ? '1px solid #91caff' : '1px solid #f0f0f0',
                                                    padding: '12px 16px',
                                                    position: 'relative',
                                                    overflow: 'hidden'
                                                },
                                                hoverable: true,
                                                onMouseEnter: (e)=>{
                                                    if (actualCurrentTeamId !== item.id) {
                                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                                        e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                                                    }
                                                },
                                                onMouseLeave: (e)=>{
                                                    if (actualCurrentTeamId !== item.id) {
                                                        e.currentTarget.style.transform = 'translateY(0)';
                                                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                                                    }
                                                },
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                    gutter: [
                                                        8,
                                                        8
                                                    ],
                                                    align: "middle",
                                                    style: {
                                                        width: '100%'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 24,
                                                            sm: 24,
                                                            md: 14,
                                                            lg: 12,
                                                            xl: 14,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                gap: 6,
                                                                className: "team-info-wrap",
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 8,
                                                                        wrap: "wrap",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    cursor: 'pointer',
                                                                                    padding: '2px 4px',
                                                                                    borderRadius: 4,
                                                                                    transition: 'all 0.2s ease',
                                                                                    display: 'flex',
                                                                                    alignItems: 'center',
                                                                                    gap: 6
                                                                                },
                                                                                onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                                onMouseEnter: (e)=>{
                                                                                    e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';
                                                                                },
                                                                                onMouseLeave: (e)=>{
                                                                                    e.currentTarget.style.background = 'transparent';
                                                                                },
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        strong: true,
                                                                                        style: {
                                                                                            fontSize: 16,
                                                                                            color: actualCurrentTeamId === item.id ? '#1890ff' : '#262626',
                                                                                            lineHeight: 1.2
                                                                                        },
                                                                                        children: item.name
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 399,
                                                                                        columnNumber: 33
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {
                                                                                        style: {
                                                                                            fontSize: 10,
                                                                                            color: actualCurrentTeamId === item.id ? '#1890ff' : '#8c8c8c',
                                                                                            verticalAlign: 'middle',
                                                                                            display: 'inline-flex',
                                                                                            alignItems: 'center'
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 412,
                                                                                        columnNumber: 33
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 377,
                                                                                columnNumber: 31
                                                                            }, void 0),
                                                                            actualCurrentTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                                style: {
                                                                                    background: '#1890ff',
                                                                                    color: 'white',
                                                                                    padding: '1px 6px',
                                                                                    borderRadius: 8,
                                                                                    fontSize: 10,
                                                                                    fontWeight: 500
                                                                                },
                                                                                children: "当前"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 428,
                                                                                columnNumber: 33
                                                                            }, void 0),
                                                                            switchingTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                align: "center",
                                                                                gap: 4,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                                        size: "small"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 446,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        style: {
                                                                                            fontSize: 10,
                                                                                            color: '#666'
                                                                                        },
                                                                                        children: "切换中"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 447,
                                                                                        columnNumber: 35
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 445,
                                                                                columnNumber: 33
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 376,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 12,
                                                                        wrap: "wrap",
                                                                        className: "team-meta-info",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                                title: `团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    align: "center",
                                                                                    gap: 4,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                                            style: {
                                                                                                color: '#8c8c8c',
                                                                                                fontSize: 12
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 460,
                                                                                            columnNumber: 35
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 12,
                                                                                                color: '#8c8c8c'
                                                                                            },
                                                                                            children: [
                                                                                                "创建: ",
                                                                                                new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 463,
                                                                                            columnNumber: 35
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                    lineNumber: 459,
                                                                                    columnNumber: 33
                                                                                }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 456,
                                                                                columnNumber: 31
                                                                            }, void 0),
                                                                            item.assignedAt && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                                title: `加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`,
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    align: "center",
                                                                                    gap: 4,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                            style: {
                                                                                                color: '#8c8c8c',
                                                                                                fontSize: 12
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 479,
                                                                                            columnNumber: 37
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 12,
                                                                                                color: '#8c8c8c'
                                                                                            },
                                                                                            children: [
                                                                                                "加入: ",
                                                                                                new Date(item.assignedAt).toLocaleDateString('zh-CN')
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 482,
                                                                                            columnNumber: 37
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                    lineNumber: 478,
                                                                                    columnNumber: 35
                                                                                }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 475,
                                                                                columnNumber: 33
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                                title: `团队成员: ${item.memberCount}人`,
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                    align: "center",
                                                                                    gap: 4,
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                                            style: {
                                                                                                color: '#8c8c8c',
                                                                                                fontSize: 12
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 497,
                                                                                            columnNumber: 35
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                            style: {
                                                                                                fontSize: 12,
                                                                                                color: '#8c8c8c'
                                                                                            },
                                                                                            children: [
                                                                                                item.memberCount,
                                                                                                " 人"
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 500,
                                                                                            columnNumber: 35
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                    lineNumber: 496,
                                                                                    columnNumber: 33
                                                                                }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 493,
                                                                                columnNumber: 31
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 455,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 8,
                                                                        wrap: "wrap",
                                                                        className: "team-status-badges",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                                style: {
                                                                                    background: item.isCreator ? '#722ed1' : '#52c41a',
                                                                                    color: 'white',
                                                                                    padding: '2px 6px',
                                                                                    borderRadius: 8,
                                                                                    fontSize: 10,
                                                                                    fontWeight: 500,
                                                                                    display: 'flex',
                                                                                    alignItems: 'center',
                                                                                    gap: 2
                                                                                },
                                                                                children: item.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                                            style: {
                                                                                                fontSize: 9
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 529,
                                                                                            columnNumber: 37
                                                                                        }, void 0),
                                                                                        "管理员"
                                                                                    ]
                                                                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                            style: {
                                                                                                fontSize: 9
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 534,
                                                                                            columnNumber: 37
                                                                                        }, void 0),
                                                                                        "成员"
                                                                                    ]
                                                                                }, void 0, true)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 512,
                                                                                columnNumber: 31
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                                style: {
                                                                                    background: item.isActive ? '#52c41a' : '#ff4d4f',
                                                                                    color: 'white',
                                                                                    padding: '2px 6px',
                                                                                    borderRadius: 8,
                                                                                    fontSize: 10,
                                                                                    fontWeight: 500,
                                                                                    display: 'flex',
                                                                                    alignItems: 'center',
                                                                                    gap: 2
                                                                                },
                                                                                children: item.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                                            style: {
                                                                                                fontSize: 9
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 556,
                                                                                            columnNumber: 37
                                                                                        }, void 0),
                                                                                        "启用"
                                                                                    ]
                                                                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MinusCircleOutlined, {
                                                                                            style: {
                                                                                                fontSize: 9
                                                                                            }
                                                                                        }, void 0, false, {
                                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                            lineNumber: 561,
                                                                                            columnNumber: 37
                                                                                        }, void 0),
                                                                                        "停用"
                                                                                    ]
                                                                                }, void 0, true)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 541,
                                                                                columnNumber: 31
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 510,
                                                                        columnNumber: 29
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 374,
                                                                columnNumber: 27
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 373,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 24,
                                                            sm: 24,
                                                            md: 10,
                                                            lg: 12,
                                                            xl: 10,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                                gutter: [
                                                                    4,
                                                                    4
                                                                ],
                                                                justify: {
                                                                    xs: 'start',
                                                                    md: 'end'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                        xs: 6,
                                                                        sm: 6,
                                                                        md: 6,
                                                                        lg: 6,
                                                                        xl: 6,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: '#f0f7ff',
                                                                                border: '1px solid #d9e8ff',
                                                                                borderRadius: 6,
                                                                                padding: '4px 6px',
                                                                                textAlign: 'center',
                                                                                minWidth: '45px'
                                                                            },
                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                vertical: true,
                                                                                align: "center",
                                                                                gap: 1,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                                        style: {
                                                                                            color: '#1890ff',
                                                                                            fontSize: 12
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 589,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        strong: true,
                                                                                        style: {
                                                                                            fontSize: 14,
                                                                                            color: '#1890ff',
                                                                                            lineHeight: 1
                                                                                        },
                                                                                        children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 592,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        style: {
                                                                                            fontSize: 8,
                                                                                            color: '#666'
                                                                                        },
                                                                                        children: "车辆"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 602,
                                                                                        columnNumber: 35
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 588,
                                                                                columnNumber: 33
                                                                            }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 578,
                                                                            columnNumber: 31
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 577,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                        xs: 6,
                                                                        sm: 6,
                                                                        md: 6,
                                                                        lg: 6,
                                                                        xl: 6,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: '#f6ffed',
                                                                                border: '1px solid #d1f0be',
                                                                                borderRadius: 6,
                                                                                padding: '4px 6px',
                                                                                textAlign: 'center',
                                                                                minWidth: '45px'
                                                                            },
                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                vertical: true,
                                                                                align: "center",
                                                                                gap: 1,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                        style: {
                                                                                            color: '#52c41a',
                                                                                            fontSize: 12
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 622,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        strong: true,
                                                                                        style: {
                                                                                            fontSize: 14,
                                                                                            color: '#52c41a',
                                                                                            lineHeight: 1
                                                                                        },
                                                                                        children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 625,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        style: {
                                                                                            fontSize: 8,
                                                                                            color: '#666'
                                                                                        },
                                                                                        children: "人员"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 635,
                                                                                        columnNumber: 35
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 621,
                                                                                columnNumber: 33
                                                                            }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 611,
                                                                            columnNumber: 31
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 610,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                        xs: 6,
                                                                        sm: 6,
                                                                        md: 6,
                                                                        lg: 6,
                                                                        xl: 6,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: '#fff7e6',
                                                                                border: '1px solid #ffd666',
                                                                                borderRadius: 6,
                                                                                padding: '4px 6px',
                                                                                textAlign: 'center',
                                                                                minWidth: '45px'
                                                                            },
                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                vertical: true,
                                                                                align: "center",
                                                                                gap: 1,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                                        style: {
                                                                                            color: '#faad14',
                                                                                            fontSize: 12
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 655,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        strong: true,
                                                                                        style: {
                                                                                            fontSize: 14,
                                                                                            color: '#faad14',
                                                                                            lineHeight: 1
                                                                                        },
                                                                                        children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 658,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        style: {
                                                                                            fontSize: 8,
                                                                                            color: '#666'
                                                                                        },
                                                                                        children: "临期"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 668,
                                                                                        columnNumber: 35
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 654,
                                                                                columnNumber: 33
                                                                            }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 644,
                                                                            columnNumber: 31
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 643,
                                                                        columnNumber: 29
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                        xs: 6,
                                                                        sm: 6,
                                                                        md: 6,
                                                                        lg: 6,
                                                                        xl: 6,
                                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                background: '#fff1f0',
                                                                                border: '1px solid #ffccc7',
                                                                                borderRadius: 6,
                                                                                padding: '4px 6px',
                                                                                textAlign: 'center',
                                                                                minWidth: '45px'
                                                                            },
                                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                                vertical: true,
                                                                                align: "center",
                                                                                gap: 1,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                                        style: {
                                                                                            color: '#ff4d4f',
                                                                                            fontSize: 12
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 688,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        strong: true,
                                                                                        style: {
                                                                                            fontSize: 14,
                                                                                            color: '#ff4d4f',
                                                                                            lineHeight: 1
                                                                                        },
                                                                                        children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 691,
                                                                                        columnNumber: 35
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                        style: {
                                                                                            fontSize: 8,
                                                                                            color: '#666'
                                                                                        },
                                                                                        children: "逾期"
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                        lineNumber: 701,
                                                                                        columnNumber: 35
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 687,
                                                                                columnNumber: 33
                                                                            }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 677,
                                                                            columnNumber: 31
                                                                        }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 676,
                                                                        columnNumber: 29
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 572,
                                                                columnNumber: 27
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 571,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                    lineNumber: 367,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                lineNumber: 327,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                            lineNumber: 326,
                                            columnNumber: 19
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 323,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 313,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 281,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true);
            };
            _s(TeamListCard, "EjENsxIjVT2688AR0sDMEuQHXlI=", false, function() {
                return [
                    _max.useModel
                ];
            });
            _c = TeamListCard;
            var _default = TeamListCard;
            var _c;
            $RefreshReg$(_c, "TeamListCard");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '16156447681387318459';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.7709086716822406584.hot-update.js.map