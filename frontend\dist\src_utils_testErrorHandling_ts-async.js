((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/utils/testErrorHandling.ts'],
{ "src/utils/testErrorHandling.ts": function (module, exports, __mako_require__){
/**
 * 错误处理测试工具
 * 
 * 用于测试和验证错误处理机制是否正常工作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    test403Error: function() {
        return test403Error;
    },
    testAllErrorCodes: function() {
        return testAllErrorCodes;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _errorHandler = __mako_require__("src/utils/errorHandler.ts");
var _responseCodes = __mako_require__("src/constants/responseCodes.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const test403Error = ()=>{
    const mockResponse = {
        code: _responseCodes.ResponseCode.FORBIDDEN,
        message: '您的账户已在此团队中被停用',
        data: null,
        timestamp: new Date().toISOString()
    };
    console.log('测试403错误处理:', mockResponse);
    (0, _errorHandler.handleApiError)(mockResponse);
};
const testAllErrorCodes = ()=>{
    const testCases = [
        {
            code: _responseCodes.ResponseCode.BAD_REQUEST,
            message: '请求参数错误'
        },
        {
            code: _responseCodes.ResponseCode.UNAUTHORIZED,
            message: '登录已过期，请重新登录'
        },
        {
            code: _responseCodes.ResponseCode.FORBIDDEN,
            message: '您的账户已在此团队中被停用'
        },
        {
            code: _responseCodes.ResponseCode.NOT_FOUND,
            message: '团队不存在'
        },
        {
            code: _responseCodes.ResponseCode.CONFLICT,
            message: '邮箱已被注册'
        },
        {
            code: _responseCodes.ResponseCode.UNPROCESSABLE_ENTITY,
            message: '验证码错误'
        },
        {
            code: _responseCodes.ResponseCode.TOO_MANY_REQUESTS,
            message: '请求频率过高，请60秒后重试'
        },
        {
            code: _responseCodes.ResponseCode.INTERNAL_SERVER_ERROR,
            message: '数据库连接失败'
        }
    ];
    testCases.forEach((testCase, index)=>{
        setTimeout(()=>{
            const mockResponse = {
                code: testCase.code,
                message: testCase.message,
                data: null,
                timestamp: new Date().toISOString()
            };
            console.log(`测试错误代码 ${testCase.code}:`, mockResponse);
            (0, _errorHandler.handleApiError)(mockResponse);
        }, index * 1000); // 每秒显示一个错误
    });
};
/**
 * 在浏览器控制台中暴露测试函数
 */ if (typeof window !== 'undefined') {
    window.testErrorHandling = {
        test403Error,
        testAllErrorCodes
    };
    console.log('错误处理测试工具已加载，可以在控制台中使用：');
    console.log('- window.testErrorHandling.test403Error() // 测试403错误');
    console.log('- window.testErrorHandling.testAllErrorCodes() // 测试所有错误代码');
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_utils_testErrorHandling_ts-async.js.map