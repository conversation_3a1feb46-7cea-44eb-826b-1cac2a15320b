{"version": 3, "sources": ["umi.750508250767452519.hot-update.js", "src/services/subscription.ts", "src/constants/responseCodes.ts", "src/utils/errorHandler.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='7174054161160553442';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 订阅管理相关 API 服务\n */\n\nimport type {\n  CreateSubscriptionRequest,\n  SubscriptionPlanResponse,\n  SubscriptionResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 订阅服务类\n */\nexport class SubscriptionService {\n  /**\n   * 获取所有订阅套餐（公开接口）\n   */\n  static async getAllPlans(): Promise<SubscriptionPlanResponse[]> {\n    const response = await apiRequest.get<SubscriptionPlanResponse[]>('/plans');\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 获取活跃的订阅套餐\n   */\n  static async getActivePlans(): Promise<SubscriptionPlanResponse[]> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    return allPlans.filter((plan) => plan.isActive);\n  }\n\n  /**\n   * 根据 ID 获取订阅套餐详情\n   */\n  static async getPlanById(planId: number): Promise<SubscriptionPlanResponse> {\n    const allPlans = await SubscriptionService.getAllPlans();\n    const plan = allPlans.find((p) => p.id === planId);\n\n    if (!plan) {\n      throw new Error('订阅套餐不存在');\n    }\n\n    return plan;\n  }\n\n  /**\n   * 获取当前用户的有效订阅\n   */\n  static async getCurrentSubscription(): Promise<SubscriptionResponse | null> {\n    try {\n      const response = await apiRequest.get<SubscriptionResponse>(\n        '/subscriptions/current',\n      );\n      return response.data;\n    } catch (error: any) {\n      // 如果没有订阅，返回 null\n      if (error?.response?.status === 404) {\n        return null;\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 创建订阅\n   */\n  static async createSubscription(\n    data: CreateSubscriptionRequest,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      '/subscriptions',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取用户订阅列表\n   */\n  static async getUserSubscriptions(): Promise<SubscriptionResponse[]> {\n    const response =\n      await apiRequest.get<SubscriptionResponse[]>('/subscriptions');\n    return response.data;\n  }\n\n  /**\n   * 取消订阅\n   */\n  static async cancelSubscription(subscriptionId: number): Promise<void> {\n    const response = await apiRequest.post<void>(\n      `/subscriptions/${subscriptionId}/cancel`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 续费订阅\n   */\n  static async renewSubscription(\n    subscriptionId: number,\n    duration: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/renew`,\n      { duration },\n    );\n    return response.data;\n  }\n\n  /**\n   * 升级订阅套餐\n   */\n  static async upgradeSubscription(\n    subscriptionId: number,\n    newPlanId: number,\n  ): Promise<SubscriptionResponse> {\n    const response = await apiRequest.post<SubscriptionResponse>(\n      `/subscriptions/${subscriptionId}/upgrade`,\n      { planId: newPlanId },\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取订阅使用统计\n   */\n  static async getSubscriptionUsage(): Promise<{\n    currentUsage: number;\n    maxUsage: number;\n    usagePercentage: number;\n    remainingDays: number;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        currentUsage: 0,\n        maxUsage: 0,\n        usagePercentage: 0,\n        remainingDays: 0,\n      };\n    }\n\n    // 计算剩余天数\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const remainingDays = Math.max(\n      0,\n      Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)),\n    );\n\n    // 这里需要后端提供实际的使用量数据\n    // 暂时返回模拟数据\n    const currentUsage = 0; // 实际使用量\n    const maxUsage = currentSubscription.maxSize;\n    const usagePercentage = maxUsage > 0 ? (currentUsage / maxUsage) * 100 : 0;\n\n    return {\n      currentUsage,\n      maxUsage,\n      usagePercentage,\n      remainingDays,\n    };\n  }\n\n  /**\n   * 获取订阅历史记录\n   */\n  static async getSubscriptionHistory(): Promise<SubscriptionResponse[]> {\n    const subscriptions = await SubscriptionService.getUserSubscriptions();\n\n    // 按创建时间倒序排列\n    return subscriptions.sort(\n      (a, b) =>\n        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),\n    );\n  }\n\n  /**\n   * 计算套餐价格（考虑折扣等）\n   */\n  static calculatePlanPrice(\n    plan: SubscriptionPlanResponse,\n    duration: number,\n  ): {\n    originalPrice: number;\n    discountedPrice: number;\n    discount: number;\n    totalPrice: number;\n  } {\n    const originalPrice = plan.price * duration;\n    let discount = 0;\n\n    // 根据订阅时长给予折扣\n    if (duration >= 12) {\n      discount = 0.2; // 年付8折\n    } else if (duration >= 6) {\n      discount = 0.1; // 半年付9折\n    }\n\n    const discountedPrice = originalPrice * (1 - discount);\n\n    return {\n      originalPrice,\n      discountedPrice,\n      discount: discount * 100,\n      totalPrice: discountedPrice,\n    };\n  }\n\n  /**\n   * 比较套餐功能\n   */\n  static comparePlans(plans: SubscriptionPlanResponse[]): Array<{\n    feature: string;\n    values: Array<string | number | boolean>;\n  }> {\n    return [\n      {\n        feature: '数据存储上限',\n        values: plans.map((plan) => plan.maxSize),\n      },\n      {\n        feature: '月费价格',\n        values: plans.map((plan) => `¥${plan.price}`),\n      },\n      {\n        feature: '技术支持',\n        values: plans.map((plan) => (plan.price > 0 ? '7x24小时' : '工作日')),\n      },\n      {\n        feature: '数据备份',\n        values: plans.map((plan) => plan.price > 0),\n      },\n      {\n        feature: '高级功能',\n        values: plans.map((plan) => plan.price >= 100),\n      },\n    ];\n  }\n\n  /**\n   * 检查订阅状态\n   */\n  static async checkSubscriptionStatus(): Promise<{\n    hasActiveSubscription: boolean;\n    isExpiringSoon: boolean;\n    daysUntilExpiry: number;\n    needsUpgrade: boolean;\n  }> {\n    const currentSubscription =\n      await SubscriptionService.getCurrentSubscription();\n\n    if (!currentSubscription) {\n      return {\n        hasActiveSubscription: false,\n        isExpiringSoon: false,\n        daysUntilExpiry: 0,\n        needsUpgrade: false,\n      };\n    }\n\n    const endDate = new Date(currentSubscription.endDate);\n    const now = new Date();\n    const daysUntilExpiry = Math.ceil(\n      (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),\n    );\n    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n\n    // 检查是否需要升级（基于使用量）\n    const usage = await SubscriptionService.getSubscriptionUsage();\n    const needsUpgrade = usage.usagePercentage > 80;\n\n    return {\n      hasActiveSubscription: true,\n      isExpiringSoon,\n      daysUntilExpiry,\n      needsUpgrade,\n    };\n  }\n}\n\n// 导出默认实例\nexport default SubscriptionService;\n", "/**\n * 标准化响应代码常量\n * \n * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。\n * 所有API响应处理都应该使用这些预定义的响应代码。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\n// ============= 成功状态码 =============\n\n/**\n * 操作成功\n */\nexport const SUCCESS = 200;\n\n// ============= 客户端错误状态码 =============\n\n/**\n * 请求参数错误或业务逻辑错误\n */\nexport const BAD_REQUEST = 400;\n\n/**\n * 未认证，需要登录\n */\nexport const UNAUTHORIZED = 401;\n\n/**\n * 权限不足，已认证但无权限访问\n */\nexport const FORBIDDEN = 403;\n\n/**\n * 资源不存在\n */\nexport const NOT_FOUND = 404;\n\n/**\n * 资源冲突（如重复创建、数据冲突等）\n */\nexport const CONFLICT = 409;\n\n/**\n * 请求格式正确但语义错误（如验证失败）\n */\nexport const UNPROCESSABLE_ENTITY = 422;\n\n/**\n * 请求频率限制\n */\nexport const TOO_MANY_REQUESTS = 429;\n\n// ============= 服务器错误状态码 =============\n\n/**\n * 服务器内部错误\n */\nexport const INTERNAL_SERVER_ERROR = 500;\n\n/**\n * 网关错误\n */\nexport const BAD_GATEWAY = 502;\n\n/**\n * 服务不可用\n */\nexport const SERVICE_UNAVAILABLE = 503;\n\n/**\n * 网关超时\n */\nexport const GATEWAY_TIMEOUT = 504;\n\n// ============= 响应代码分类方法 =============\n\n/**\n * 判断是否为成功响应代码\n * \n * @param code 响应代码\n * @returns 是否为成功代码\n */\nexport const isSuccess = (code: number): boolean => {\n  return code === SUCCESS;\n};\n\n/**\n * 判断是否为客户端错误代码\n * \n * @param code 响应代码\n * @returns 是否为客户端错误代码\n */\nexport const isClientError = (code: number): boolean => {\n  return code >= 400 && code < 500;\n};\n\n/**\n * 判断是否为服务器错误代码\n * \n * @param code 响应代码\n * @returns 是否为服务器错误代码\n */\nexport const isServerError = (code: number): boolean => {\n  return code >= 500 && code < 600;\n};\n\n/**\n * 判断是否为错误代码（非成功代码）\n * \n * @param code 响应代码\n * @returns 是否为错误代码\n */\nexport const isError = (code: number): boolean => {\n  return !isSuccess(code);\n};\n\n// ============= 响应代码描述方法 =============\n\n/**\n * 获取响应代码的默认描述\n * \n * @param code 响应代码\n * @returns 响应代码描述\n */\nexport const getDescription = (code: number): string => {\n  switch (code) {\n    case SUCCESS:\n      return '操作成功';\n    case BAD_REQUEST:\n      return '请求参数错误';\n    case UNAUTHORIZED:\n      return '未认证，需要登录';\n    case FORBIDDEN:\n      return '权限不足';\n    case NOT_FOUND:\n      return '资源不存在';\n    case CONFLICT:\n      return '资源冲突';\n    case UNPROCESSABLE_ENTITY:\n      return '请求语义错误';\n    case TOO_MANY_REQUESTS:\n      return '请求频率过高';\n    case INTERNAL_SERVER_ERROR:\n      return '服务器内部错误';\n    case BAD_GATEWAY:\n      return '网关错误';\n    case SERVICE_UNAVAILABLE:\n      return '服务不可用';\n    case GATEWAY_TIMEOUT:\n      return '网关超时';\n    default:\n      return '未知错误';\n  }\n};\n\n// ============= 响应代码集合 =============\n\n/**\n * 所有成功响应代码\n */\nexport const SUCCESS_CODES = [SUCCESS];\n\n/**\n * 所有客户端错误响应代码\n */\nexport const CLIENT_ERROR_CODES = [\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n];\n\n/**\n * 所有服务器错误响应代码\n */\nexport const SERVER_ERROR_CODES = [\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n];\n\n/**\n * 所有错误响应代码\n */\nexport const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];\n\n/**\n * 所有响应代码\n */\nexport const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];\n\n// ============= 默认导出 =============\n\n/**\n * 响应代码常量对象\n */\nexport const ResponseCode = {\n  // 成功状态码\n  SUCCESS,\n  \n  // 客户端错误状态码\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n  \n  // 服务器错误状态码\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n  \n  // 工具方法\n  isSuccess,\n  isClientError,\n  isServerError,\n  isError,\n  getDescription,\n  \n  // 代码集合\n  SUCCESS_CODES,\n  CLIENT_ERROR_CODES,\n  SERVER_ERROR_CODES,\n  ERROR_CODES,\n  ALL_CODES,\n} as const;\n\nexport default ResponseCode;\n", "/**\n * 集中式错误消息处理工具\n * \n * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。\n * 支持message和notification组件，确保错误消息显示的一致性。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { ResponseCode, isError, getDescription } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\nimport { AuthService } from '@/services';\n\n// ============= 错误显示类型 =============\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 'silent',\n  /** 使用message.warning显示 */\n  WARNING = 'warning',\n  /** 使用message.error显示 */\n  ERROR = 'error',\n  /** 使用notification显示 */\n  NOTIFICATION = 'notification',\n  /** 重定向到登录页 */\n  REDIRECT = 'redirect',\n}\n\n// ============= 错误处理配置 =============\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否显示详细错误信息 */\n  showDetails?: boolean;\n  /** 错误回调函数 */\n  onError?: (error: any) => void;\n}\n\n/**\n * 默认错误处理配置\n */\nconst DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {\n  [ResponseCode.BAD_REQUEST]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.UNAUTHORIZED]: {\n    displayType: ErrorDisplayType.REDIRECT,\n  },\n  [ResponseCode.FORBIDDEN]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.NOT_FOUND]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.CONFLICT]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.UNPROCESSABLE_ENTITY]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.TOO_MANY_REQUESTS]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.INTERNAL_SERVER_ERROR]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务器内部错误，请稍后重试',\n  },\n  [ResponseCode.BAD_GATEWAY]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '网关错误，请稍后重试',\n  },\n  [ResponseCode.SERVICE_UNAVAILABLE]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务暂时不可用，请稍后重试',\n  },\n  [ResponseCode.GATEWAY_TIMEOUT]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '请求超时，请稍后重试',\n  },\n};\n\n// ============= 核心错误处理方法 =============\n\n/**\n * 处理API响应错误\n * \n * @param response API响应对象\n * @param config 错误处理配置\n */\nexport const handleApiError = (\n  response: ApiResponse<any>,\n  config?: ErrorHandlerConfig\n): void => {\n  if (!response || ResponseCode.isSuccess(response.code)) {\n    return;\n  }\n\n  const errorCode = response.code;\n  const errorMessage = response.message || getDescription(errorCode);\n\n  // 调试日志\n  console.log('handleApiError 被调用:', {\n    errorCode,\n    errorMessage,\n    response,\n    config\n  });\n\n  // 合并配置\n  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {\n    displayType: ErrorDisplayType.ERROR,\n  };\n  const finalConfig = { ...defaultConfig, ...config };\n\n  // 使用自定义消息或响应消息\n  const displayMessage = finalConfig.customMessage || errorMessage;\n\n  console.log('错误处理配置:', {\n    defaultConfig,\n    finalConfig,\n    displayMessage\n  });\n\n  // 执行错误回调\n  if (finalConfig.onError) {\n    finalConfig.onError({ code: errorCode, message: errorMessage, response });\n  }\n\n  // 根据显示类型处理错误\n  switch (finalConfig.displayType) {\n    case ErrorDisplayType.SILENT:\n      console.log('静默处理错误');\n      break;\n\n    case ErrorDisplayType.WARNING:\n      console.log('显示警告消息:', displayMessage);\n      message.warning(displayMessage);\n      break;\n\n    case ErrorDisplayType.ERROR:\n      console.log('显示错误消息:', displayMessage);\n      message.error(displayMessage);\n      break;\n\n    case ErrorDisplayType.NOTIFICATION:\n      console.log('显示通知:', displayMessage);\n      notification.error({\n        message: '操作失败',\n        description: displayMessage,\n        duration: 4.5,\n      });\n      break;\n\n    case ErrorDisplayType.REDIRECT:\n      console.log('处理认证错误:', errorCode, displayMessage);\n      handleAuthError(errorCode, displayMessage);\n      break;\n\n    default:\n      console.log('默认错误处理:', displayMessage);\n      message.error(displayMessage);\n      break;\n  }\n};\n\n/**\n * 处理认证相关错误\n * \n * @param errorCode 错误代码\n * @param errorMessage 错误消息\n */\nconst handleAuthError = (errorCode: number, errorMessage: string): void => {\n  console.log('handleAuthError 被调用:', { errorCode, errorMessage });\n\n  if (errorCode === ResponseCode.UNAUTHORIZED) {\n    // 检查当前路径，避免在某些页面立即跳转\n    const currentPath = window.location.pathname;\n    const isDashboardRelated =\n      currentPath.startsWith('/dashboard') ||\n      currentPath.startsWith('/team');\n\n    console.log('401错误处理:', { currentPath, isDashboardRelated });\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      return;\n    }\n\n    // 清除认证信息并跳转到登录页\n    AuthService.clearTokens();\n    console.log('显示401错误消息并跳转登录页:', errorMessage);\n    message.error(errorMessage || '登录已过期，请重新登录');\n    history.push('/user/login');\n  } else if (errorCode === ResponseCode.FORBIDDEN) {\n    console.log('显示403错误消息:', errorMessage);\n    message.error(errorMessage || '没有权限访问该资源');\n  }\n};\n\n// ============= 便捷方法 =============\n\n/**\n * 显示成功消息\n * \n * @param message 成功消息\n */\nexport const showSuccess = (msg: string): void => {\n  message.success(msg);\n};\n\n/**\n * 显示警告消息\n * \n * @param message 警告消息\n */\nexport const showWarning = (msg: string): void => {\n  message.warning(msg);\n};\n\n/**\n * 显示错误消息\n * \n * @param message 错误消息\n */\nexport const showError = (msg: string): void => {\n  message.error(msg);\n};\n\n/**\n * 显示信息消息\n * \n * @param message 信息消息\n */\nexport const showInfo = (msg: string): void => {\n  message.info(msg);\n};\n\n/**\n * 显示通知\n * \n * @param title 通知标题\n * @param description 通知描述\n * @param type 通知类型\n */\nexport const showNotification = (\n  title: string,\n  description: string,\n  type: 'success' | 'info' | 'warning' | 'error' = 'info'\n): void => {\n  notification[type]({\n    message: title,\n    description,\n    duration: 4.5,\n  });\n};\n\n// ============= 错误处理Hook =============\n\n/**\n * 创建错误处理器\n * \n * @param config 默认错误处理配置\n * @returns 错误处理函数\n */\nexport const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {\n  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {\n    const finalConfig = { ...defaultConfig, ...config };\n    handleApiError(response, finalConfig);\n  };\n};\n\n// ============= 默认导出 =============\n\nexport default {\n  handleApiError,\n  showSuccess,\n  showWarning,\n  showError,\n  showInfo,\n  showNotification,\n  createErrorHandler,\n  ErrorDisplayType,\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCaA,mBAAmB;2BAAnB;;gBAoRb,SAAS;gBACT,OAAmC;2BAAnC;;;;;4CA5R2B;kDACE;iDACE;;;;;;;;;YAKxB,MAAM;gBACX;;GAEC,GACD,aAAa,cAAmD;oBAC9D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA6B;oBAElE,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,iBAAsD;oBACjE,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,OAAO,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;gBAChD;gBAEA;;GAEC,GACD,aAAa,YAAY,MAAc,EAAqC;oBAC1E,MAAM,WAAW,MAAM,oBAAoB,WAAW;oBACtD,MAAM,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAE3C,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;oBAGlB,OAAO;gBACT;gBAEA;;GAEC,GACD,aAAa,yBAA+D;oBAC1E,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;wBAEF,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAO,OAAY;4BAEf;wBADJ,iBAAiB;wBACjB,IAAI,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,MAAK,KAC9B,OAAO;wBAET,MAAM;oBACR;gBACF;gBAEA;;GAEC,GACD,aAAa,mBACX,IAA+B,EACA;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAAwD;oBACnE,MAAM,WACJ,MAAM,mBAAU,CAAC,GAAG,CAAyB;oBAC/C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,mBAAmB,cAAsB,EAAiB;oBACrE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,OAAO,CAAC;oBAE3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBACX,cAAsB,EACtB,QAAgB,EACe;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,EACxC;wBAAE;oBAAS;oBAEb,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,oBACX,cAAsB,EACtB,SAAiB,EACc;oBAC/B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,eAAe,EAAE,eAAe,QAAQ,CAAC,EAC1C;wBAAE,QAAQ;oBAAU;oBAEtB,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,cAAc;wBACd,UAAU;wBACV,iBAAiB;wBACjB,eAAe;oBACjB;oBAGF,SAAS;oBACT,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,gBAAgB,KAAK,GAAG,CAC5B,GACA,KAAK,IAAI,CAAC,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAGnD,mBAAmB;oBACnB,WAAW;oBACX,MAAM,eAAe,GAAG,QAAQ;oBAChC,MAAM,WAAW,oBAAoB,OAAO;oBAC5C,MAAM,kBAAkB,WAAW,IAAI,AAAC,eAAe,WAAY,MAAM;oBAEzE,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF;gBAEA;;GAEC,GACD,aAAa,yBAA0D;oBACrE,MAAM,gBAAgB,MAAM,oBAAoB,oBAAoB;oBAEpE,YAAY;oBACZ,OAAO,cAAc,IAAI,CACvB,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBAErE;gBAEA;;GAEC,GACD,OAAO,mBACL,IAA8B,EAC9B,QAAgB,EAMhB;oBACA,MAAM,gBAAgB,KAAK,KAAK,GAAG;oBACnC,IAAI,WAAW;oBAEf,aAAa;oBACb,IAAI,YAAY,IACd,WAAW,KAAK,OAAO;yBAClB,IAAI,YAAY,GACrB,WAAW,KAAK,QAAQ;oBAG1B,MAAM,kBAAkB,gBAAiB,CAAA,IAAI,QAAO;oBAEpD,OAAO;wBACL;wBACA;wBACA,UAAU,WAAW;wBACrB,YAAY;oBACd;gBACF;gBAEA;;GAEC,GACD,OAAO,aAAa,KAAiC,EAGlD;oBACD,OAAO;wBACL;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;wBAC1C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;wBAC9C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAU,KAAK,KAAK,GAAG,IAAI,WAAW;wBAC3D;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,GAAG;wBAC3C;wBACA;4BACE,SAAS;4BACT,QAAQ,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,IAAI;wBAC5C;qBACD;gBACH;gBAEA;;GAEC,GACD,aAAa,0BAKV;oBACD,MAAM,sBACJ,MAAM,oBAAoB,sBAAsB;oBAElD,IAAI,CAAC,qBACH,OAAO;wBACL,uBAAuB;wBACvB,gBAAgB;wBAChB,iBAAiB;wBACjB,cAAc;oBAChB;oBAGF,MAAM,UAAU,IAAI,KAAK,oBAAoB,OAAO;oBACpD,MAAM,MAAM,IAAI;oBAChB,MAAM,kBAAkB,KAAK,IAAI,CAC/B,AAAC,CAAA,QAAQ,OAAO,KAAK,IAAI,OAAO,EAAC,IAAM;oBAEzC,MAAM,iBAAiB,mBAAmB,KAAK,kBAAkB;oBAEjE,kBAAkB;oBAClB,MAAM,QAAQ,MAAM,oBAAoB,oBAAoB;oBAC5D,MAAM,eAAe,MAAM,eAAe,GAAG;oBAE7C,OAAO;wBACL,uBAAuB;wBACvB;wBACA;wBACA;oBACF;gBACF;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBClGF,SAAS;2BAAT;;gBAnIA,WAAW;2BAAX;;gBA1CA,WAAW;2BAAX;;gBAiJA,kBAAkB;2BAAlB;;gBA7HA,QAAQ;2BAAR;;gBAoJA,WAAW;2BAAX;;gBA9JA,SAAS;2BAAT;;gBA0CA,eAAe;2BAAf;;gBAfA,qBAAqB;2BAArB;;gBAtBA,SAAS;2BAAT;;gBAqKA,YAAY;2BAAZ;;gBAtBA,kBAAkB;2BAAlB;;gBA/GA,mBAAmB;2BAAnB;;gBAtDA,OAAO;2BAAP;;gBAmJA,aAAa;2BAAb;;gBA9GA,iBAAiB;2BAAjB;;gBAzBA,YAAY;2BAAZ;;gBAoBA,oBAAoB;2BAApB;;gBA6Lb,OAA4B;2BAA5B;;gBA9Ga,cAAc;2BAAd;;gBAhCA,aAAa;2BAAb;;gBAoBA,OAAO;2BAAP;;gBAVA,aAAa;2BAAb;;gBApBA,SAAS;2BAAT;;;;;;;;;;;;;YArEN,MAAM,UAAU;YAOhB,MAAM,cAAc;YAKpB,MAAM,eAAe;YAKrB,MAAM,YAAY;YAKlB,MAAM,YAAY;YAKlB,MAAM,WAAW;YAKjB,MAAM,uBAAuB;YAK7B,MAAM,oBAAoB;YAO1B,MAAM,wBAAwB;YAK9B,MAAM,cAAc;YAKpB,MAAM,sBAAsB;YAK5B,MAAM,kBAAkB;YAUxB,MAAM,YAAY,CAAC;gBACxB,OAAO,SAAS;YAClB;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,UAAU,CAAC;gBACtB,OAAO,CAAC,UAAU;YACpB;YAUO,MAAM,iBAAiB,CAAC;gBAC7B,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE,OAAO;gBACX;YACF;YAOO,MAAM,gBAAgB;gBAAC;aAAQ;YAK/B,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,cAAc;mBAAI;mBAAuB;aAAmB;YAKlE,MAAM,YAAY;mBAAI;mBAAkB;aAAY;YAOpD,MAAM,eAAe;gBAC1B,QAAQ;gBACR;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCwCF,kBAAkB;2BAAlB;;gBAOb,mCAAmC;gBAEnC,OASE;2BATF;;gBAxLa,cAAc;2BAAd;;gBAuIA,SAAS;2BAAT;;gBASA,QAAQ;2BAAR;;gBAWA,gBAAgB;2BAAhB;;gBAtCA,WAAW;2BAAX;;gBASA,WAAW;2BAAX;;;;;yCAzNyB;wCACd;kDAC8B;6CAE1B;;;;;;;;;;sBAOhB;gBACV,iBAAiB;gBAEjB,wBAAwB;gBAExB,sBAAsB;gBAEtB,qBAAqB;gBAErB,YAAY;eATF,qBAAA;YA6BZ;;CAEC,GACD,MAAM,uBAA2D;gBAC/D,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,YAAY,CAAC,EAAE;oBAC3B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,QAAQ,CAAC,EAAE;oBACvB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,oBAAoB,CAAC,EAAE;oBACnC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,iBAAiB,CAAC,EAAE;oBAChC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,qBAAqB,CAAC,EAAE;oBACpC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,mBAAmB,CAAC,EAAE;oBAClC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,eAAe,CAAC,EAAE;oBAC9B,WAAW;oBACX,eAAe;gBACjB;YACF;YAUO,MAAM,iBAAiB,CAC5B,UACA;gBAEA,IAAI,CAAC,YAAY,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACnD;gBAGF,MAAM,YAAY,SAAS,IAAI;gBAC/B,MAAM,eAAe,SAAS,OAAO,IAAI,IAAA,6BAAc,EAAC;gBAExD,OAAO;gBACP,QAAQ,GAAG,CAAC,uBAAuB;oBACjC;oBACA;oBACA;oBACA;gBACF;gBAEA,OAAO;gBACP,MAAM,gBAAgB,oBAAoB,CAAC,UAAU,IAAI;oBACvD,WAAW;gBACb;gBACA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,eAAe;gBACf,MAAM,iBAAiB,YAAY,aAAa,IAAI;gBAEpD,QAAQ,GAAG,CAAC,WAAW;oBACrB;oBACA;oBACA;gBACF;gBAEA,SAAS;gBACT,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC;oBAAE,MAAM;oBAAW,SAAS;oBAAc;gBAAS;gBAGzE,aAAa;gBACb,OAAQ,YAAY,WAAW;oBAC7B;wBACE,QAAQ,GAAG,CAAC;wBACZ;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,OAAO,CAAC;wBAChB;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;oBAEF;wBACE,QAAQ,GAAG,CAAC,SAAS;wBACrB,kBAAY,CAAC,KAAK,CAAC;4BACjB,SAAS;4BACT,aAAa;4BACb,UAAU;wBACZ;wBACA;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW,WAAW;wBAClC,gBAAgB,WAAW;wBAC3B;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;gBACJ;YACF;YAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,WAAmB;gBAC1C,QAAQ,GAAG,CAAC,wBAAwB;oBAAE;oBAAW;gBAAa;gBAE9D,IAAI,cAAc,2BAAY,CAAC,YAAY,EAAE;oBAC3C,qBAAqB;oBACrB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oBAEzB,QAAQ,GAAG,CAAC,YAAY;wBAAE;wBAAa;oBAAmB;oBAE1D,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb;oBACF;oBAEA,gBAAgB;oBAChB,qBAAW,CAAC,WAAW;oBACvB,QAAQ,GAAG,CAAC,oBAAoB;oBAChC,aAAO,CAAC,KAAK,CAAC,gBAAgB;oBAC9B,YAAO,CAAC,IAAI,CAAC;gBACf,OAAO,IAAI,cAAc,2BAAY,CAAC,SAAS,EAAE;oBAC/C,QAAQ,GAAG,CAAC,cAAc;oBAC1B,aAAO,CAAC,KAAK,CAAC,gBAAgB;gBAChC;YACF;YASO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,YAAY,CAAC;gBACxB,aAAO,CAAC,KAAK,CAAC;YAChB;YAOO,MAAM,WAAW,CAAC;gBACvB,aAAO,CAAC,IAAI,CAAC;YACf;YASO,MAAM,mBAAmB,CAC9B,OACA,aACA,OAAiD,MAAM;gBAEvD,kBAAY,CAAC,KAAK,CAAC;oBACjB,SAAS;oBACT;oBACA,UAAU;gBACZ;YACF;YAUO,MAAM,qBAAqB,CAAC;gBACjC,OAAO,CAAC,UAA4B;oBAClC,MAAM,cAAc;wBAAE,GAAG,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAClD,eAAe,UAAU;gBAC3B;YACF;gBAIA,WAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;;;;;;;;;;;;;;;;;;;;;;;IHnSc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}