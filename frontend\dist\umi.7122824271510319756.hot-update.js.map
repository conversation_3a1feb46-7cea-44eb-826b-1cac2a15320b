{"version": 3, "sources": ["umi.7122824271510319756.hot-update.js", "src/services/invitation.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4627203728002574714';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队邀请相关 API 服务\n */\n\nimport type {\n  TeamInvitationResponse,\n  RespondInvitationRequest,\n  SendInvitationResponse,\n  AcceptInvitationByLinkRequest,\n  AcceptInvitationByLinkResponse,\n  InvitationInfoResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 邀请服务类\n */\nexport class InvitationService {\n  /**\n   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）\n   */\n  static async getCurrentTeamInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/teams/current/invitations');\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的邀请列表（需要 Account Token）\n   */\n  static async getUserReceivedInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/received');\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 获取用户收到的待处理邀请列表（需要 Account Token）\n   */\n  static async getUserPendingInvitations(): Promise<TeamInvitationResponse[]> {\n    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/pending');\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 响应邀请（需要 Account Token）\n   */\n  static async respondToInvitation(\n    invitationId: number,\n    data: RespondInvitationRequest,\n  ): Promise<void> {\n    const response = await apiRequest.post<void>(`/invitations/${invitationId}/respond`, data);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n  }\n\n  /**\n   * 取消邀请（需要 Team Token，仅邀请人）\n   */\n  static async cancelInvitation(invitationId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(`/invitations/${invitationId}`);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n  }\n\n  /**\n   * 获取邀请详情\n   */\n  static async getInvitationDetail(invitationId: number): Promise<TeamInvitationResponse> {\n    const response = await apiRequest.get<TeamInvitationResponse>(`/invitations/${invitationId}`);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 发送邀请并生成邀请链接（需要 Team Token，仅创建者）\n   */\n  static async sendInvitations(data: { emails: string[]; message?: string }): Promise<SendInvitationResponse> {\n    const response = await apiRequest.post<SendInvitationResponse>('/invitations/send', data);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 获取邀请信息（公开接口）\n   */\n  static async getInvitationInfo(token: string): Promise<InvitationInfoResponse> {\n    const response = await apiRequest.get<InvitationInfoResponse>(\n      `/invitations/info/${token}`\n    );\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 通过邀请链接接受邀请（公开接口）\n   */\n  static async acceptInvitationByLink(\n    token: string,\n    data: AcceptInvitationByLinkRequest\n  ): Promise<AcceptInvitationByLinkResponse> {\n    const response = await apiRequest.post<AcceptInvitationByLinkResponse>(\n      `/invitations/accept-by-link/${token}`,\n      data\n    );\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 更新过期邀请状态（系统内部接口）\n   */\n  static async updateExpiredInvitations(): Promise<number> {\n    const response = await apiRequest.post<number>('/invitations/system/update-expired');\n    return response.data;\n  }\n}\n\n// 默认导出\nexport default InvitationService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCgBA,iBAAiB;2BAAjB;;gBAwJb,OAAO;gBACP,OAAiC;2BAAjC;;;;;4CAhK2B;kDACE;iDACE;;;;;;;;;YAKxB,MAAM;gBACX;;GAEC,GACD,aAAa,4BAA+D;oBAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;oBAEhE,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,6BAAgE;oBAC3E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;oBAEhE,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,4BAA+D;oBAC1E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAA2B;oBAEhE,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,oBACX,YAAoB,EACpB,IAA8B,EACf;oBACf,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAO,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,EAAE;oBAErF,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;gBACF;gBAEA;;GAEC,GACD,aAAa,iBAAiB,YAAoB,EAAiB;oBACjE,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CAAO,CAAC,aAAa,EAAE,aAAa,CAAC;oBAE7E,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;gBACF;gBAEA;;GAEC,GACD,aAAa,oBAAoB,YAAoB,EAAmC;oBACtF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAyB,CAAC,aAAa,EAAE,aAAa,CAAC;oBAE5F,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,gBAAgB,IAA4C,EAAmC;oBAC1G,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAyB,qBAAqB;oBAEpF,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBAAkB,KAAa,EAAmC;oBAC7E,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,kBAAkB,EAAE,MAAM,CAAC;oBAG9B,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBACX,KAAa,EACb,IAAmC,EACM;oBACzC,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,CAAC,4BAA4B,EAAE,MAAM,CAAC,EACtC;oBAGF,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,2BAA4C;oBACvD,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAS;oBAC/C,OAAO,SAAS,IAAI;gBACtB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDzKD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}