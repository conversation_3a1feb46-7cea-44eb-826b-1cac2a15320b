globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/subscription.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                SubscriptionService: function() {
                    return SubscriptionService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class SubscriptionService {
                /**
   * 获取所有订阅套餐（公开接口）
   */ static async getAllPlans() {
                    const response = await _request.apiRequest.get('/plans');
                    return response.data;
                }
                /**
   * 获取活跃的订阅套餐
   */ static async getActivePlans() {
                    const allPlans = await SubscriptionService.getAllPlans();
                    return allPlans.filter((plan)=>plan.isActive);
                }
                /**
   * 根据 ID 获取订阅套餐详情
   */ static async getPlanById(planId) {
                    const allPlans = await SubscriptionService.getAllPlans();
                    const plan = allPlans.find((p)=>p.id === planId);
                    if (!plan) throw new Error('订阅套餐不存在');
                    return plan;
                }
                /**
   * 获取当前用户的有效订阅
   */ static async getCurrentSubscription() {
                    try {
                        const response = await _request.apiRequest.get('/subscriptions/current');
                        return response.data;
                    } catch (error) {
                        var _error_response;
                        // 如果没有订阅，返回 null
                        if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) return null;
                        throw error;
                    }
                }
                /**
   * 创建订阅
   */ static async createSubscription(data) {
                    const response = await _request.apiRequest.post('/subscriptions', data);
                    return response.data;
                }
                /**
   * 获取用户订阅列表
   */ static async getUserSubscriptions() {
                    const response = await _request.apiRequest.get('/subscriptions');
                    return response.data;
                }
                /**
   * 取消订阅
   */ static async cancelSubscription(subscriptionId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/cancel`);
                    return response.data;
                }
                /**
   * 续费订阅
   */ static async renewSubscription(subscriptionId, duration) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/renew`, {
                        duration
                    });
                    return response.data;
                }
                /**
   * 升级订阅套餐
   */ static async upgradeSubscription(subscriptionId, newPlanId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/upgrade`, {
                        planId: newPlanId
                    });
                    return response.data;
                }
                /**
   * 获取订阅使用统计
   */ static async getSubscriptionUsage() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        currentUsage: 0,
                        maxUsage: 0,
                        usagePercentage: 0,
                        remainingDays: 0
                    };
                    // 计算剩余天数
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / 86400000));
                    // 这里需要后端提供实际的使用量数据
                    // 暂时返回模拟数据
                    const currentUsage = 0; // 实际使用量
                    const maxUsage = currentSubscription.maxSize;
                    const usagePercentage = maxUsage > 0 ? currentUsage / maxUsage * 100 : 0;
                    return {
                        currentUsage,
                        maxUsage,
                        usagePercentage,
                        remainingDays
                    };
                }
                /**
   * 获取订阅历史记录
   */ static async getSubscriptionHistory() {
                    const subscriptions = await SubscriptionService.getUserSubscriptions();
                    // 按创建时间倒序排列
                    return subscriptions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                }
                /**
   * 计算套餐价格（考虑折扣等）
   */ static calculatePlanPrice(plan, duration) {
                    const originalPrice = plan.price * duration;
                    let discount = 0;
                    // 根据订阅时长给予折扣
                    if (duration >= 12) discount = 0.2; // 年付8折
                    else if (duration >= 6) discount = 0.1; // 半年付9折
                    const discountedPrice = originalPrice * (1 - discount);
                    return {
                        originalPrice,
                        discountedPrice,
                        discount: discount * 100,
                        totalPrice: discountedPrice
                    };
                }
                /**
   * 比较套餐功能
   */ static comparePlans(plans) {
                    return [
                        {
                            feature: '数据存储上限',
                            values: plans.map((plan)=>plan.maxSize)
                        },
                        {
                            feature: '月费价格',
                            values: plans.map((plan)=>`¥${plan.price}`)
                        },
                        {
                            feature: '技术支持',
                            values: plans.map((plan)=>plan.price > 0 ? '7x24小时' : '工作日')
                        },
                        {
                            feature: '数据备份',
                            values: plans.map((plan)=>plan.price > 0)
                        },
                        {
                            feature: '高级功能',
                            values: plans.map((plan)=>plan.price >= 100)
                        }
                    ];
                }
                /**
   * 检查订阅状态
   */ static async checkSubscriptionStatus() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        hasActiveSubscription: false,
                        isExpiringSoon: false,
                        daysUntilExpiry: 0,
                        needsUpgrade: false
                    };
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / 86400000);
                    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;
                    // 检查是否需要升级（基于使用量）
                    const usage = await SubscriptionService.getSubscriptionUsage();
                    const needsUpgrade = usage.usagePercentage > 80;
                    return {
                        hasActiveSubscription: true,
                        isExpiringSoon,
                        daysUntilExpiry,
                        needsUpgrade
                    };
                }
            }
            var _default = SubscriptionService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
            var _team = __mako_require__("src/services/team.ts");
            var _invitation = __mako_require__("src/services/invitation.ts");
            var _api = __mako_require__("src/types/api.ts");
            var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
                const [inviteForm] = _antd.Form.useForm();
                // 邀请管理相关状态
                const [invitations, setInvitations] = (0, _react.useState)([]);
                const [invitationLoading, setInvitationLoading] = (0, _react.useState)(false);
                const [invitationSearchText, setInvitationSearchText] = (0, _react.useState)('');
                const [statusFilter, setStatusFilter] = (0, _react.useState)('');
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                    fetchInvitations();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        _antd.message.error('获取团队成员失败');
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 获取邀请列表
                const fetchInvitations = async ()=>{
                    try {
                        setInvitationLoading(true);
                        const invitationList = await _invitation.InvitationService.getCurrentTeamInvitations();
                        setInvitations(invitationList || []);
                    } catch (error) {
                        console.error('获取邀请列表失败:', error);
                        _antd.message.error('获取邀请列表失败');
                        setInvitations([]);
                    } finally{
                        setInvitationLoading(false);
                    }
                };
                // 邀请新成员
                const handleInviteMembers = async (values)=>{
                    try {
                        const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
                        // 使用新的邀请链接功能
                        const response = await _invitation.InvitationService.sendInvitations({
                            emails: emailList,
                            message: values.message
                        });
                        // 显示详细的发送结果
                        if (response.successCount > 0) {
                            _antd.message.success(`成功发送 ${response.successCount} 个邀请`);
                            console.log('邀请链接:', response.invitations.map((inv)=>({
                                    email: inv.email,
                                    link: inv.invitationLink
                                })));
                        }
                        if (response.failureCount > 0) _antd.message.warning(`${response.failureCount} 个邀请发送失败`);
                        setInviteModalVisible(false);
                        inviteForm.resetFields();
                        fetchMembers();
                        fetchInvitations(); // 刷新邀请列表
                        onRefresh(); // 刷新团队详情
                    } catch (error) {
                        console.error('邀请成员失败:', error);
                        _antd.message.error('邀请成员失败');
                    }
                };
                // 取消邀请
                const handleCancelInvitation = async (invitationId)=>{
                    try {
                        await _invitation.InvitationService.cancelInvitation(invitationId);
                        _antd.message.success('邀请取消成功');
                        fetchInvitations();
                        onRefresh();
                    } catch (error) {
                        console.error('取消邀请失败:', error);
                        _antd.message.error('取消邀请失败');
                    }
                };
                // 移除单个成员
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        _antd.message.success(`已移除成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('移除成员失败:', error);
                        _antd.message.error('移除成员失败');
                    }
                };
                // 批量移除成员
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        _antd.message.success(`已移除 ${memberIds.length} 名成员`);
                        setSelectedRowKeys([]);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('批量移除成员失败:', error);
                        _antd.message.error('批量移除成员失败');
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 停用/启用成员
                const handleToggleMemberStatus = async (member, isActive)=>{
                    try {
                        await _team.TeamService.updateMemberStatus(member.id, isActive);
                        _antd.message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('更新成员状态失败:', error);
                        _antd.message.error('更新成员状态失败');
                    }
                };
                // 表格列配置
                const columns = [
                    {
                        title: '姓名',
                        dataIndex: 'name',
                        key: 'name',
                        render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: name
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 231,
                                        columnNumber: 11
                                    }, this),
                                    record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 233,
                                            columnNumber: 24
                                        }, void 0),
                                        color: "gold",
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 233,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 230,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邮箱',
                        dataIndex: 'email',
                        key: 'email',
                        render: (email)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: email
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 243,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'red',
                                children: isActive ? '启用' : '停用'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 252,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 277,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, false),
                                        children: "停用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 283,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, true),
                                        children: "启用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 291,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                        title: "确认移除成员",
                                        description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                        onConfirm: ()=>handleRemoveMember(record),
                                        okText: "确认",
                                        cancelText: "取消",
                                        okType: "danger",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            danger: true,
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 311,
                                                columnNumber: 23
                                            }, void 0),
                                            children: "移除"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 307,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 299,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 281,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                // 行选择配置
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                // 邀请表格列定义
                const invitationColumns = [
                    {
                        title: '被邀请人',
                        key: 'invitee',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: 0,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: record.inviteeName || '未注册用户'
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 338,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: '12px'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 340,
                                                columnNumber: 13
                                            }, this),
                                            " ",
                                            record.inviteeEmail
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 339,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 337,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邀请状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                                status: status,
                                isExpired: record.isExpired
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 350,
                                columnNumber: 9
                            }, this),
                        filters: [
                            {
                                text: '待确认',
                                value: _api.InvitationStatus.PENDING
                            },
                            {
                                text: '已接受',
                                value: _api.InvitationStatus.ACCEPTED
                            },
                            {
                                text: '已拒绝',
                                value: _api.InvitationStatus.REJECTED
                            },
                            {
                                text: '已过期',
                                value: _api.InvitationStatus.EXPIRED
                            },
                            {
                                text: '已取消',
                                value: _api.InvitationStatus.CANCELLED
                            }
                        ],
                        onFilter: (value, record)=>record.status === value
                    },
                    {
                        title: '邀请时间',
                        dataIndex: 'invitedAt',
                        key: 'invitedAt',
                        render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 369,
                                columnNumber: 9
                            }, this),
                        sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
                    },
                    {
                        title: '过期时间',
                        dataIndex: 'expiresAt',
                        key: 'expiresAt',
                        render: (time, record)=>{
                            const isExpired = record.isExpired;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: isExpired ? 'danger' : 'secondary',
                                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 383,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 382,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (_, record)=>{
                            if (record.status === _api.InvitationStatus.PENDING && !record.isExpired) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确定要取消这个邀请吗？",
                                onConfirm: ()=>handleCancelInvitation(record.id),
                                okText: "确定",
                                cancelText: "取消",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    size: "small",
                                    danger: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 402,
                                        columnNumber: 49
                                    }, void 0),
                                    children: "取消邀请"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 402,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 396,
                                columnNumber: 13
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 408,
                                columnNumber: 16
                            }, this);
                        }
                    }
                ];
                // 过滤邀请列表
                const filteredInvitations = invitations.filter((invitation)=>{
                    const matchesSearch = !invitationSearchText || invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase());
                    const matchesStatus = !statusFilter || invitation.status === statusFilter;
                    return matchesSearch && matchesStatus;
                });
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 430,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: [
                                            "团队成员 (",
                                            (members || []).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 431,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 429,
                                columnNumber: 11
                            }, void 0),
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        style: {
                                            width: '100%',
                                            justifyContent: 'space-between'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "搜索成员姓名或邮箱",
                                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 442,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        value: searchText,
                                                        onChange: (e)=>setSearchText(e.target.value),
                                                        style: {
                                                            width: 250
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 440,
                                                        columnNumber: 15
                                                    }, this),
                                                    selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                        title: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`,
                                                        onConfirm: handleBatchRemove,
                                                        okText: "确定",
                                                        cancelText: "取消",
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            danger: true,
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 454,
                                                                columnNumber: 40
                                                            }, void 0),
                                                            children: [
                                                                "批量移除 (",
                                                                selectedRowKeys.length,
                                                                ")"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 454,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 448,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 439,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "primary",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 462,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: ()=>setInviteModalVisible(true),
                                                children: "邀请成员"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 460,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 438,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 437,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: columns,
                                    dataSource: filteredMembers,
                                    rowKey: "id",
                                    loading: loading,
                                    rowSelection: rowSelection,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 名成员`,
                                        pageSize: 10
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 471,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 427,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 490,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: [
                                            "邀请记录 (",
                                            (invitations || []).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 491,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 489,
                                columnNumber: 11
                            }, void 0),
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        style: {
                                            width: '100%',
                                            justifyContent: 'space-between'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "搜索邮箱或姓名",
                                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 501,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        value: invitationSearchText,
                                                        onChange: (e)=>setInvitationSearchText(e.target.value),
                                                        style: {
                                                            width: 200
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 499,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                        placeholder: "筛选状态",
                                                        value: statusFilter,
                                                        onChange: setStatusFilter,
                                                        style: {
                                                            width: 120
                                                        },
                                                        allowClear: true,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.PENDING,
                                                                children: "待确认"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 513,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.ACCEPTED,
                                                                children: "已接受"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 514,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.REJECTED,
                                                                children: "已拒绝"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 515,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.EXPIRED,
                                                                children: "已过期"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 516,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.CANCELLED,
                                                                children: "已取消"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 517,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 506,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 498,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 521,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: fetchInvitations,
                                                loading: invitationLoading,
                                                children: "刷新"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 520,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 497,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 496,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: invitationColumns,
                                    dataSource: filteredInvitations,
                                    rowKey: "id",
                                    loading: invitationLoading,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 条邀请记录`,
                                        pageSize: 10
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 531,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 487,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "邀请新成员",
                            open: inviteModalVisible,
                            onCancel: ()=>{
                                setInviteModalVisible(false);
                                inviteForm.resetFields();
                            },
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: inviteForm,
                                layout: "vertical",
                                onFinish: handleInviteMembers,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "emails",
                                        label: "邮箱地址",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            }
                                        ],
                                        extra: "每行一个邮箱地址，支持批量邀请",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 6,
                                            placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 571,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 563,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "message",
                                        label: "邀请消息（可选）",
                                        extra: "您可以添加一些邀请消息，让被邀请人更好地了解邀请意图",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 3,
                                            placeholder: "欢迎加入我们的团队！我们期待与您一起工作...",
                                            maxLength: 500,
                                            showCount: true
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 581,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 576,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 590,
                                                        columnNumber: 62
                                                    }, void 0),
                                                    children: "发送邀请"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 590,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setInviteModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 593,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 589,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 588,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 558,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 548,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 425,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "clJXlt+1N7F8WykBT8WZbIbsLSc=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/user/login/index.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _components = __mako_require__("src/components/index.ts");
            var _services = __mako_require__("src/services/index.ts");
            var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            var _s1 = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            // 登录表单组件（移到外部避免重新创建）
            const LoginFormComponent = /*#__PURE__*/ _react.default.memo(_s(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading })=>{
                _s();
                // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染
                const sendCodeButton = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "link",
                        size: "small",
                        disabled: countdown > 0 || sendingCode,
                        loading: sendingCode,
                        onClick: handleSendCode,
                        style: {
                            padding: 0,
                            height: 'auto'
                        },
                        children: countdown > 0 ? `${countdown}s后重发` : '发送验证码'
                    }, void 0, false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 37,
                        columnNumber: 5
                    }, this), [
                    countdown,
                    sendingCode,
                    handleSendCode
                ]);
                // 使用 useMemo 稳定邮箱输入框，避免重新渲染
                const emailField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "email",
                        rules: [
                            {
                                required: true,
                                message: '请输入邮箱！'
                            },
                            {
                                type: 'email',
                                message: '请输入有效的邮箱地址！'
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 61,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "邮箱",
                            autoComplete: "email"
                        }, "email-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 59,
                            columnNumber: 7
                        }, this)
                    }, "email-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 51,
                        columnNumber: 5
                    }, this), []);
                // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染
                const codeField = (0, _react.useMemo)(()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                        name: "code",
                        rules: [
                            {
                                required: true,
                                message: '请输入验证码！'
                            },
                            {
                                len: 6,
                                message: '验证码为6位数字！'
                            },
                            {
                                pattern: /^\d{6}$/,
                                message: '验证码只能包含数字！'
                            }
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SafetyOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 81,
                                columnNumber: 17
                            }, void 0),
                            placeholder: "6位验证码",
                            maxLength: 6,
                            suffix: sendCodeButton
                        }, "code-input", false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 79,
                            columnNumber: 7
                        }, this)
                    }, "code-field", false, {
                        fileName: "src/pages/user/login/index.tsx",
                        lineNumber: 70,
                        columnNumber: 5
                    }, this), [
                    sendCodeButton
                ]);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    name: "login",
                    size: "large",
                    onFinish: handleLogin,
                    autoComplete: "off",
                    children: [
                        emailField,
                        codeField,
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                textAlign: 'center'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                style: {
                                    fontSize: '12px'
                                },
                                children: "新用户将自动完成注册并登录"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 102,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 101,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                htmlType: "submit",
                                loading: loading,
                                block: true,
                                children: "登录 / 注册"
                            }, void 0, false, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 108,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 107,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 90,
                    columnNumber: 5
                }, this);
            }, "vH8AbKPV2dOQkAxU7xEfdsmzClM="));
            _c = LoginFormComponent;
            const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
                return {
                    container: {
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100vh',
                        overflow: 'auto',
                        backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
                        backgroundSize: '100% 100%'
                    },
                    content: {
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: '32px 16px'
                    },
                    header: {
                        marginBottom: 40,
                        textAlign: 'center'
                    },
                    logo: {
                        marginBottom: 16
                    },
                    title: {
                        marginBottom: 0
                    },
                    loginCard: {
                        width: '100%',
                        maxWidth: 400,
                        boxShadow: token.boxShadowTertiary
                    },
                    footer: {
                        marginTop: 40,
                        textAlign: 'center'
                    },
                    lang: {
                        width: 42,
                        height: 42,
                        lineHeight: '42px',
                        position: 'fixed',
                        right: 16,
                        top: 16,
                        borderRadius: token.borderRadius,
                        ':hover': {
                            backgroundColor: token.colorBgTextHover
                        }
                    }
                };
            });
            const LoginPage = ()=>{
                _s1();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [sendingCode, setSendingCode] = (0, _react.useState)(false);
                const [countdown, setCountdown] = (0, _react.useState)(0);
                const [form] = _antd.Form.useForm(); // 将表单实例提升到父组件
                const { styles } = useStyles();
                const { setInitialState } = (0, _max.useModel)('@@initialState');
                // 使用 Form 内置的邮箱验证
                // 组件挂载时清除倒计时状态，避免页面刷新后无法输入
                (0, _react.useEffect)(()=>{
                    setCountdown(0);
                }, []);
                // 倒计时效果
                _react.default.useEffect(()=>{
                    let timer;
                    if (countdown > 0) timer = setTimeout(()=>{
                        setCountdown(countdown - 1);
                    }, 1000);
                    return ()=>{
                        if (timer) clearTimeout(timer);
                    };
                }, [
                    countdown
                ]);
                // 发送验证码
                const handleSendCode = (0, _react.useCallback)(async (type = 'login')=>{
                    let email;
                    try {
                        // 验证邮箱字段
                        await form.validateFields([
                            'email'
                        ]);
                        // 从表单获取邮箱值
                        email = form.getFieldValue('email');
                        console.log('发送验证码前的邮箱值:', email);
                        if (!email) {
                            _antd.message.error('请输入邮箱地址');
                            return;
                        }
                    } catch (error) {
                        // 表单验证失败
                        _antd.message.error('请输入有效的邮箱地址');
                        return;
                    }
                    setSendingCode(true);
                    try {
                        const request = {
                            email,
                            type
                        };
                        const response = await _services.AuthService.sendVerificationCode(request);
                        if (response.success) {
                            _antd.message.success(response.message);
                            setCountdown(60); // 60秒倒计时
                            // 验证码发送成功后检查表单值
                            console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));
                            _antd.message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);
                        } else {
                            _antd.message.error(response.message);
                            if (response.nextSendTime) setCountdown(response.nextSendTime);
                        }
                    } catch (error) {
                        console.error('发送验证码失败:', error);
                        _antd.message.error('发送验证码失败，请稍后重试');
                    } finally{
                        setSendingCode(false);
                    }
                }, [
                    form
                ]);
                // 处理登录/注册
                const handleLogin = (0, _react.useCallback)(async (values)=>{
                    setLoading(true);
                    try {
                        const response = await _services.AuthService.login(values);
                        _antd.message.success('登录成功！');
                        // 登录成功后停止倒计时
                        setCountdown(0);
                        // 登录成功后，刷新 initialState
                        await setInitialState((prevState)=>({
                                ...prevState,
                                currentUser: response.user,
                                currentTeam: response.teams.length > 0 ? response.teams[0] : undefined
                            }));
                        // 等待一小段时间确保状态更新完成
                        await new Promise((resolve)=>setTimeout(resolve, 100));
                        // 根据团队数量进行不同的跳转处理
                        if (response.teams.length === 0) // 没有团队，跳转到个人中心页面
                        _max.history.push('/personal-center');
                        else // 有团队（无论一个还是多个），都跳转到个人中心整合页面
                        _max.history.push('/personal-center', {
                            teams: response.teams
                        });
                    } catch (error) {
                        console.error('登录失败:', error);
                    } finally{
                        setLoading(false);
                    }
                }, [
                    setInitialState
                ]);
                // 注册功能已移除，统一使用验证码登录/注册流程
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    className: styles.container,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                                children: [
                                    "登录 / 注册",
                                    _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/user/login/index.tsx",
                                lineNumber: 289,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 288,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: styles.content,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.header,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        align: "center",
                                        size: "large",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.logo,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                                    src: "/logo.svg",
                                                    alt: "TeamAuth",
                                                    height: 48
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/login/index.tsx",
                                                    lineNumber: 298,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 297,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                className: styles.title,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 2,
                                                        children: "团队管理系统"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 301,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: "现代化的团队协作与管理平台"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/user/login/index.tsx",
                                                        lineNumber: 302,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/user/login/index.tsx",
                                                lineNumber: 300,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 296,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 295,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: styles.loginCard,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(LoginFormComponent, {
                                        form: form,
                                        handleLogin: handleLogin,
                                        handleSendCode: ()=>handleSendCode('login'),
                                        sendingCode: sendingCode,
                                        countdown: countdown,
                                        loading: loading
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 308,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 307,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.footer,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "© 2025 TeamAuth. All rights reserved."
                                    }, void 0, false, {
                                        fileName: "src/pages/user/login/index.tsx",
                                        lineNumber: 319,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/login/index.tsx",
                                    lineNumber: 318,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 294,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/pages/user/login/index.tsx",
                            lineNumber: 322,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/login/index.tsx",
                    lineNumber: 287,
                    columnNumber: 5
                }, this);
            };
            _s1(LoginPage, "Yq4Fb8fQ18048Y8KY3QhqRjuF9Y=", false, function() {
                return [
                    _antd.Form.useForm,
                    useStyles,
                    _max.useModel
                ];
            });
            _c1 = LoginPage;
            var _default = LoginPage;
            var _c;
            var _c1;
            $RefreshReg$(_c, "LoginFormComponent");
            $RefreshReg$(_c1, "LoginPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '46133676004068872';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.2485812050730738212.hot-update.js.map