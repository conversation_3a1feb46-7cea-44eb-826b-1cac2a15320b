{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.7296330664369699398.hot-update.js", "src/pages/personal-center/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='9082175385887449971';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "import { useModel, history } from '@umijs/max';\nimport { Card, Col, Row, Spin } from 'antd';\nimport React, { useEffect } from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport UserProfileCard from './UserProfileCard';\n\nconst PersonalCenterPage: React.FC = () => {\n  const { initialState, loading } = useModel('@@initialState');\n\n  // 如果正在加载，显示加载状态\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  // 如果用户未登录，跳转到登录页\n  useEffect(() => {\n    if (!loading && !initialState?.currentUser) {\n      history.push('/user/login');\n    }\n  }, [loading, initialState?.currentUser]);\n\n  // 如果用户未登录，不渲染页面内容（避免闪烁）\n  if (!loading && !initialState?.currentUser) {\n    return null;\n  }\n\n  return (\n    <>\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          padding: '12px 12px 24px 12px', // 移动端减少左右边距\n        }}\n      >\n        {/* 大的容器区域 */}\n        <Card\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)',\n            borderRadius: '12px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n          }}\n          styles={{\n            body: {\n              padding: '24px',\n            },\n          }}\n        >\n        <Row gutter={[16, 16]} style={{ margin: 0 }}>\n          {/* 个人信息卡片 - 全宽显示 */}\n          <Col xs={24} style={{ marginBottom: 8 }}>\n            <UserProfileCard />\n          </Col>\n\n          {/* 待办事项 - 响应式布局 */}\n          <Col\n            xs={24}\n            sm={24}\n            md={24}\n            lg={12}\n            xl={12}\n            xxl={12}\n            style={{ marginBottom: 8 }}\n          >\n            <TodoManagement />\n          </Col>\n\n          {/* 团队列表 - 响应式布局 */}\n          <Col xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>\n            <TeamListCard />\n          </Col>\n        </Row>\n      </Card>\n    </div>\n\n    {/* 浮动按钮 */}\n    <UserFloatButton />\n  </>\n  );\n};\n\nexport default PersonalCenterPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC8Fb;;;2BAAA;;;;;;;wCAjGkC;yCACG;oFACJ;yFACL;0FACH;4FACE;6FACC;;;;;;;;;;YAE5B,MAAM,qBAA+B;;gBACnC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAE3C,gBAAgB;gBAChB,IAAI,SACF,qBACE,2BAAC;oBACC,OAAO;wBACL,WAAW;wBACX,YAAY;wBACZ,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;;sCAEA,2BAAC,UAAI;4BAAC,MAAK;;;;;;sCACX,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAG;sCAAG;;;;;;;;;;;;gBAKtC,iBAAiB;gBACjB,IAAA,gBAAS,EAAC;oBACR,IAAI,CAAC,WAAW,EAAC,yBAAA,mCAAA,aAAc,WAAW,GACxC,YAAO,CAAC,IAAI,CAAC;gBAEjB,GAAG;oBAAC;oBAAS,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAEvC,wBAAwB;gBACxB,IAAI,CAAC,WAAW,EAAC,yBAAA,mCAAA,aAAc,WAAW,GACxC,OAAO;gBAGT,qBACE;;sCACE,2BAAC;4BACC,OAAO;gCACL,WAAW;gCACX,YAAY;gCACZ,SAAS;4BACX;sCAGA,cAAA,2BAAC,UAAI;gCACH,OAAO;oCACL,OAAO;oCACP,WAAW;oCACX,cAAc;oCACd,WAAW;gCACb;gCACA,QAAQ;oCACN,MAAM;wCACJ,SAAS;oCACX;gCACF;0CAEF,cAAA,2BAAC,SAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;oCAAE,OAAO;wCAAE,QAAQ;oCAAE;;sDAExC,2BAAC,SAAG;4CAAC,IAAI;4CAAI,OAAO;gDAAE,cAAc;4CAAE;sDACpC,cAAA,2BAAC,wBAAe;;;;;;;;;;sDAIlB,2BAAC,SAAG;4CACF,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,IAAI;4CACJ,KAAK;4CACL,OAAO;gDAAE,cAAc;4CAAE;sDAEzB,cAAA,2BAAC,uBAAc;;;;;;;;;;sDAIjB,2BAAC,SAAG;4CAAC,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,IAAI;4CAAI,KAAK;sDAChD,cAAA,2BAAC,qBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrB,2BAAC,oBAAe;;;;;;;YAGpB;eAvFM;;oBAC8B,aAAQ;;;iBADtC;gBAyFN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID9FD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}