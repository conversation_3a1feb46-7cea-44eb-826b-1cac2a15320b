# API错误处理工具类使用指南

## 概述

`ApiErrorHandler` 是一个专门用于处理API响应错误的工具类，它提供了统一的错误检测、处理和显示机制。该工具类已经集成到项目的请求配置中，会自动处理所有API调用的错误响应。

## 主要功能

1. **自动错误检测**: 检测API响应中 `code !== 200` 的错误情况
2. **统一错误显示**: 使用Ant Design的 `message` 和 `notification` 组件显示错误
3. **特殊错误处理**: 自动处理认证(401)、权限(403)、服务器(5xx)等特殊错误
4. **自定义错误处理**: 支持自定义错误处理逻辑
5. **多种显示类型**: 支持静默、警告、错误、通知等多种显示方式

## 基本使用

### 自动处理（推荐）

由于错误处理器已经集成到请求配置中，大多数情况下你不需要手动调用：

```typescript
import { apiRequest } from '@/services';

// API调用会自动处理错误
const fetchUserData = async () => {
  try {
    const response = await apiRequest.get('/user/profile');
    // 只有成功的响应才会到达这里
    return response.data;
  } catch (error) {
    // 错误已经被自动处理和显示
    console.log('请求失败');
  }
};
```

### 手动处理

如果需要自定义错误处理逻辑：

```typescript
import { apiErrorHandler, ErrorDisplayType } from '@/utils/apiErrorHandler';

// 检查响应是否有错误
const response = await fetch('/api/some-endpoint');
const data = await response.json();

if (apiErrorHandler.isApiError(data)) {
  // 自定义错误处理
  apiErrorHandler.handleApiResponse(data, {
    displayType: ErrorDisplayType.NOTIFICATION,
    customMessage: '操作失败，请重试',
    customHandler: (errorInfo) => {
      console.log('自定义处理:', errorInfo);
    }
  });
}
```

## 错误显示类型

```typescript
enum ErrorDisplayType {
  SILENT = 0,        // 静默处理，不显示任何消息
  WARNING = 1,       // 显示警告消息
  ERROR = 2,         // 显示错误消息（默认）
  NOTIFICATION = 3,  // 显示通知
  REDIRECT = 9,      // 重定向（通常用于认证错误）
}
```

## 配置选项

```typescript
interface ErrorHandlerConfig {
  displayType?: ErrorDisplayType;           // 错误显示类型
  customMessage?: string;                   // 自定义错误消息
  skipDefaultHandler?: boolean;             // 是否跳过默认错误处理
  customHandler?: (error: ApiErrorInfo) => void; // 自定义错误处理函数
  logError?: boolean;                       // 是否在控制台输出错误详情
}
```

## 高级用法

### 设置全局默认配置

```typescript
import { apiErrorHandler } from '@/utils/apiErrorHandler';

// 设置全局默认配置
apiErrorHandler.setDefaultConfig({
  displayType: ErrorDisplayType.NOTIFICATION,
  logError: false, // 生产环境可以关闭错误日志
});
```

### 创建错误处理中间件

```typescript
import { apiErrorHandler } from '@/utils/apiErrorHandler';

// 创建自定义错误处理中间件
const customErrorMiddleware = apiErrorHandler.createErrorMiddleware({
  displayType: ErrorDisplayType.WARNING,
  customHandler: (errorInfo) => {
    // 发送错误到监控系统
    sendErrorToMonitoring(errorInfo);
  }
});

// 在特定的API调用中使用
const response = await fetch('/api/critical-operation');
const data = await response.json();
customErrorMiddleware(data);
```

### 特定场景的错误处理

```typescript
// 静默处理错误（不显示给用户）
apiErrorHandler.handleApiResponse(response, {
  displayType: ErrorDisplayType.SILENT,
  customHandler: (errorInfo) => {
    // 只记录日志，不显示给用户
    console.warn('Background operation failed:', errorInfo);
  }
});

// 显示自定义错误消息
apiErrorHandler.handleApiResponse(response, {
  customMessage: '保存失败，请检查网络连接后重试',
  displayType: ErrorDisplayType.NOTIFICATION
});
```

## 特殊错误处理

### 认证错误 (401)
- 自动清除用户Token
- 显示"登录已过期"消息
- 重定向到登录页面
- Dashboard页面有特殊处理逻辑

### 权限错误 (403)
- 显示权限不足消息
- 使用后端返回的具体错误信息

### 服务器错误 (5xx)
- 显示"服务器内部错误"消息
- 建议用户稍后重试

## 最佳实践

1. **使用自动处理**: 大多数情况下依赖自动错误处理即可
2. **自定义关键操作**: 对于重要操作，提供自定义错误消息
3. **静默处理后台操作**: 后台数据同步等操作使用静默处理
4. **记录错误日志**: 在开发环境启用错误日志，生产环境可选择性关闭
5. **用户友好的消息**: 提供清晰、可操作的错误消息

## 注意事项

1. 错误处理器已经集成到全局请求配置中，会自动处理所有API错误
2. 认证错误会自动清除Token并重定向，无需手动处理
3. 自定义处理器会在默认处理之后执行（除非设置 `skipDefaultHandler: true`）
4. 错误信息会自动记录到控制台（可通过配置关闭）

## 示例场景

### 表单提交错误处理
```typescript
const handleSubmit = async (formData) => {
  try {
    await apiRequest.post('/user/update', formData);
    message.success('保存成功');
  } catch (error) {
    // 错误已自动显示，这里可以做额外处理
    setSubmitting(false);
  }
};
```

### 数据加载错误处理
```typescript
const loadData = async () => {
  try {
    const response = await apiRequest.get('/data/list');
    setData(response.data);
  } catch (error) {
    // 错误已自动显示
    setData([]); // 设置默认值
  }
};
```

### 批量操作错误处理
```typescript
const batchOperation = async (items) => {
  const results = await Promise.allSettled(
    items.map(item => apiRequest.post('/process', item))
  );

  const failed = results.filter(r => r.status === 'rejected');
  if (failed.length > 0) {
    message.warning(`${failed.length} 个操作失败，请检查后重试`);
  }
};
```

## 示例组件

以下是一个完整的React组件示例，展示如何在实际项目中使用错误处理器：

```typescript
import React, { useState, useEffect } from 'react';
import { Button, Card, Spin, message } from 'antd';
import { apiRequest } from '@/services';
import { apiErrorHandler, ErrorDisplayType } from '@/utils/apiErrorHandler';

interface UserData {
  id: number;
  name: string;
  email: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载用户数据 - 使用自动错误处理
  const loadUsers = async () => {
    setLoading(true);
    try {
      const response = await apiRequest.get<UserData[]>('/users');
      setUsers(response.data);
    } catch (error) {
      // 错误已自动显示，这里只需要处理UI状态
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // 删除用户 - 使用自定义错误处理
  const deleteUser = async (userId: number) => {
    try {
      await apiRequest.delete(`/users/${userId}`);
      message.success('用户删除成功');
      loadUsers(); // 重新加载数据
    } catch (error) {
      // 错误已自动显示
    }
  };

  // 批量操作 - 使用静默错误处理
  const syncUserData = async () => {
    try {
      const response = await apiRequest.post('/users/sync');

      // 手动处理响应，使用静默模式
      const result = apiErrorHandler.handleApiResponse(response, {
        displayType: ErrorDisplayType.SILENT,
        customHandler: (errorInfo) => {
          console.warn('Background sync failed:', errorInfo);
          // 可以发送到监控系统
        }
      });

      if (!result) {
        // 成功
        console.log('Background sync completed');
      }
    } catch (error) {
      // 网络错误等
      console.error('Sync request failed:', error);
    }
  };

  useEffect(() => {
    loadUsers();

    // 设置定时同步
    const interval = setInterval(syncUserData, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Card title="用户管理">
      <div style={{ marginBottom: 16 }}>
        <Button onClick={loadUsers} loading={loading}>
          刷新数据
        </Button>
      </div>

      <Spin spinning={loading}>
        {users.map(user => (
          <Card
            key={user.id}
            size="small"
            style={{ marginBottom: 8 }}
            actions={[
              <Button
                type="link"
                danger
                onClick={() => deleteUser(user.id)}
              >
                删除
              </Button>
            ]}
          >
            <div>{user.name} ({user.email})</div>
          </Card>
        ))}
      </Spin>
    </Card>
  );
};

export default UserManagement;
```
