globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/subscription.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                SubscriptionService: function() {
                    return SubscriptionService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class SubscriptionService {
                /**
   * 获取所有订阅套餐（公开接口）
   */ static async getAllPlans() {
                    const response = await _request.apiRequest.get('/plans');
                    return response.data;
                }
                /**
   * 获取活跃的订阅套餐
   */ static async getActivePlans() {
                    const allPlans = await SubscriptionService.getAllPlans();
                    return allPlans.filter((plan)=>plan.isActive);
                }
                /**
   * 根据 ID 获取订阅套餐详情
   */ static async getPlanById(planId) {
                    const allPlans = await SubscriptionService.getAllPlans();
                    const plan = allPlans.find((p)=>p.id === planId);
                    if (!plan) throw new Error('订阅套餐不存在');
                    return plan;
                }
                /**
   * 获取当前用户的有效订阅
   */ static async getCurrentSubscription() {
                    try {
                        const response = await _request.apiRequest.get('/subscriptions/current');
                        return response.data;
                    } catch (error) {
                        var _error_response;
                        // 如果没有订阅，返回 null
                        if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) return null;
                        throw error;
                    }
                }
                /**
   * 创建订阅
   */ static async createSubscription(data) {
                    const response = await _request.apiRequest.post('/subscriptions', data);
                    return response.data;
                }
                /**
   * 获取用户订阅列表
   */ static async getUserSubscriptions() {
                    const response = await _request.apiRequest.get('/subscriptions');
                    return response.data;
                }
                /**
   * 取消订阅
   */ static async cancelSubscription(subscriptionId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/cancel`);
                    return response.data;
                }
                /**
   * 续费订阅
   */ static async renewSubscription(subscriptionId, duration) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/renew`, {
                        duration
                    });
                    return response.data;
                }
                /**
   * 升级订阅套餐
   */ static async upgradeSubscription(subscriptionId, newPlanId) {
                    const response = await _request.apiRequest.post(`/subscriptions/${subscriptionId}/upgrade`, {
                        planId: newPlanId
                    });
                    return response.data;
                }
                /**
   * 获取订阅使用统计
   */ static async getSubscriptionUsage() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        currentUsage: 0,
                        maxUsage: 0,
                        usagePercentage: 0,
                        remainingDays: 0
                    };
                    // 计算剩余天数
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const remainingDays = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / 86400000));
                    // 这里需要后端提供实际的使用量数据
                    // 暂时返回模拟数据
                    const currentUsage = 0; // 实际使用量
                    const maxUsage = currentSubscription.maxSize;
                    const usagePercentage = maxUsage > 0 ? currentUsage / maxUsage * 100 : 0;
                    return {
                        currentUsage,
                        maxUsage,
                        usagePercentage,
                        remainingDays
                    };
                }
                /**
   * 获取订阅历史记录
   */ static async getSubscriptionHistory() {
                    const subscriptions = await SubscriptionService.getUserSubscriptions();
                    // 按创建时间倒序排列
                    return subscriptions.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
                }
                /**
   * 计算套餐价格（考虑折扣等）
   */ static calculatePlanPrice(plan, duration) {
                    const originalPrice = plan.price * duration;
                    let discount = 0;
                    // 根据订阅时长给予折扣
                    if (duration >= 12) discount = 0.2; // 年付8折
                    else if (duration >= 6) discount = 0.1; // 半年付9折
                    const discountedPrice = originalPrice * (1 - discount);
                    return {
                        originalPrice,
                        discountedPrice,
                        discount: discount * 100,
                        totalPrice: discountedPrice
                    };
                }
                /**
   * 比较套餐功能
   */ static comparePlans(plans) {
                    return [
                        {
                            feature: '数据存储上限',
                            values: plans.map((plan)=>plan.maxSize)
                        },
                        {
                            feature: '月费价格',
                            values: plans.map((plan)=>`¥${plan.price}`)
                        },
                        {
                            feature: '技术支持',
                            values: plans.map((plan)=>plan.price > 0 ? '7x24小时' : '工作日')
                        },
                        {
                            feature: '数据备份',
                            values: plans.map((plan)=>plan.price > 0)
                        },
                        {
                            feature: '高级功能',
                            values: plans.map((plan)=>plan.price >= 100)
                        }
                    ];
                }
                /**
   * 检查订阅状态
   */ static async checkSubscriptionStatus() {
                    const currentSubscription = await SubscriptionService.getCurrentSubscription();
                    if (!currentSubscription) return {
                        hasActiveSubscription: false,
                        isExpiringSoon: false,
                        daysUntilExpiry: 0,
                        needsUpgrade: false
                    };
                    const endDate = new Date(currentSubscription.endDate);
                    const now = new Date();
                    const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / 86400000);
                    const isExpiringSoon = daysUntilExpiry <= 7 && daysUntilExpiry > 0;
                    // 检查是否需要升级（基于使用量）
                    const usage = await SubscriptionService.getSubscriptionUsage();
                    const needsUpgrade = usage.usagePercentage > 80;
                    return {
                        hasActiveSubscription: true,
                        isExpiringSoon,
                        daysUntilExpiry,
                        needsUpgrade
                    };
                }
            }
            var _default = SubscriptionService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '750508250767452519';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=umi.4627203728002574714.hot-update.js.map