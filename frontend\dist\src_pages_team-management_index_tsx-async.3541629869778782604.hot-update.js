globalThis.makoModuleHotUpdate('src/pages/team-management/index.tsx', {
    modules: {
        "src/pages/team-management/components/TeamMemberManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
            var _team = __mako_require__("src/services/team.ts");
            var _invitation = __mako_require__("src/services/invitation.ts");
            var _api = __mako_require__("src/types/api.ts");
            var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TextArea } = _antd.Input;
            const TeamMemberManagement = ({ teamDetail, onRefresh })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(false);
                const [members, setMembers] = (0, _react.useState)([]);
                const [searchText, setSearchText] = (0, _react.useState)('');
                const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
                const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
                const [inviteForm] = _antd.Form.useForm();
                // 邀请管理相关状态
                const [invitations, setInvitations] = (0, _react.useState)([]);
                const [invitationLoading, setInvitationLoading] = (0, _react.useState)(false);
                const [invitationSearchText, setInvitationSearchText] = (0, _react.useState)('');
                const [statusFilter, setStatusFilter] = (0, _react.useState)('');
                (0, _react.useEffect)(()=>{
                    fetchMembers();
                    fetchInvitations();
                }, []);
                const fetchMembers = async ()=>{
                    try {
                        setLoading(true);
                        const memberList = await _team.TeamService.getCurrentTeamMembers();
                        setMembers(memberList || []);
                    } catch (error) {
                        console.error('获取团队成员失败:', error);
                        // Error message already displayed by service layer
                        setMembers([]); // 确保在错误时设置为空数组
                    } finally{
                        setLoading(false);
                    }
                };
                // 获取邀请列表
                const fetchInvitations = async ()=>{
                    try {
                        setInvitationLoading(true);
                        const invitationList = await _invitation.InvitationService.getCurrentTeamInvitations();
                        setInvitations(invitationList || []);
                    } catch (error) {
                        console.error('获取邀请列表失败:', error);
                        // Error message already displayed by service layer
                        setInvitations([]);
                    } finally{
                        setInvitationLoading(false);
                    }
                };
                // 邀请新成员
                const handleInviteMembers = async (values)=>{
                    try {
                        const emailList = values.emails.split('\n').map((email)=>email.trim()).filter((email)=>email);
                        // 使用新的邀请链接功能
                        const response = await _invitation.InvitationService.sendInvitations({
                            emails: emailList,
                            message: values.message
                        });
                        // Service layer handles errors, so if we get here, the request was successful
                        // Display detailed results
                        if (response.successCount > 0) {
                            _antd.message.success(`成功发送 ${response.successCount} 个邀请`);
                            console.log('邀请链接:', response.invitations.map((inv)=>({
                                    email: inv.email,
                                    link: inv.invitationLink
                                })));
                        }
                        if (response.failureCount > 0) _antd.message.warning(`${response.failureCount} 个邀请发送失败`);
                        setInviteModalVisible(false);
                        inviteForm.resetFields();
                        fetchMembers();
                        fetchInvitations(); // 刷新邀请列表
                        onRefresh(); // 刷新团队详情
                    } catch (error) {
                        console.error('邀请成员失败:', error);
                    // Error message already displayed by service layer
                    }
                };
                // 取消邀请
                const handleCancelInvitation = async (invitationId)=>{
                    try {
                        await _invitation.InvitationService.cancelInvitation(invitationId);
                        _antd.message.success('邀请取消成功');
                        fetchInvitations();
                        onRefresh();
                    } catch (error) {
                        console.error('取消邀请失败:', error);
                    // Error message already displayed by service layer
                    }
                };
                // 移除单个成员
                const handleRemoveMember = async (member)=>{
                    try {
                        await _team.TeamService.removeMember(member.id);
                        _antd.message.success(`已移除成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('移除成员失败:', error);
                    // Error message already displayed by service layer
                    }
                };
                // 批量移除成员
                const handleBatchRemove = async ()=>{
                    try {
                        const memberIds = selectedRowKeys;
                        for (const memberId of memberIds)await _team.TeamService.removeMember(memberId);
                        _antd.message.success(`已移除 ${memberIds.length} 名成员`);
                        setSelectedRowKeys([]);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('批量移除成员失败:', error);
                    // Error message already displayed by service layer
                    }
                };
                // 筛选成员
                const filteredMembers = (members || []).filter((member)=>member.name.toLowerCase().includes(searchText.toLowerCase()) || member.email.toLowerCase().includes(searchText.toLowerCase()));
                // 停用/启用成员
                const handleToggleMemberStatus = async (member, isActive)=>{
                    try {
                        await _team.TeamService.updateMemberStatus(member.id, isActive);
                        _antd.message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);
                        fetchMembers();
                        onRefresh();
                    } catch (error) {
                        console.error('更新成员状态失败:', error);
                    // Error message already displayed by service layer
                    }
                };
                // 表格列配置
                const columns = [
                    {
                        title: '姓名',
                        dataIndex: 'name',
                        key: 'name',
                        render: (name, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: name
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 232,
                                        columnNumber: 11
                                    }, this),
                                    record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 234,
                                            columnNumber: 24
                                        }, void 0),
                                        color: "gold",
                                        children: "创建者"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 231,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邮箱',
                        dataIndex: 'email',
                        key: 'email',
                        render: (email)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: email
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 244,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'status',
                        width: 100,
                        render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                color: isActive ? 'green' : 'red',
                                children: isActive ? '启用' : '停用'
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 253,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '加入时间',
                        dataIndex: 'assignedAt',
                        key: 'assignedAt',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '最后访问',
                        dataIndex: 'lastAccessTime',
                        key: 'lastAccessTime',
                        width: 150,
                        render: (date)=>new Date(date).toLocaleDateString()
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: 200,
                        render: (_, record)=>{
                            if (record.isCreator) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 278,
                                columnNumber: 18
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    record.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, false),
                                        children: "停用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 284,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "text",
                                        size: "small",
                                        onClick: ()=>handleToggleMemberStatus(record, true),
                                        children: "启用"
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 292,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                        title: "确认移除成员",
                                        description: `确定要移除成员 ${record.name} 吗？此操作不可恢复。`,
                                        onConfirm: ()=>handleRemoveMember(record),
                                        okText: "确认",
                                        cancelText: "取消",
                                        okType: "danger",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            danger: true,
                                            size: "small",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 312,
                                                columnNumber: 23
                                            }, void 0),
                                            children: "移除"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 308,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 300,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 282,
                                columnNumber: 11
                            }, this);
                        }
                    }
                ];
                // 行选择配置
                const rowSelection = {
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record)=>({
                            disabled: record.isCreator
                        })
                };
                // 邀请表格列定义
                const invitationColumns = [
                    {
                        title: '被邀请人',
                        key: 'invitee',
                        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: 0,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: record.inviteeName || '未注册用户'
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 339,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        style: {
                                            fontSize: '12px'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 341,
                                                columnNumber: 13
                                            }, this),
                                            " ",
                                            record.inviteeEmail
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 340,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 338,
                                columnNumber: 9
                            }, this)
                    },
                    {
                        title: '邀请状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                                status: status,
                                isExpired: record.isExpired
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 351,
                                columnNumber: 9
                            }, this),
                        filters: [
                            {
                                text: '待确认',
                                value: _api.InvitationStatus.PENDING
                            },
                            {
                                text: '已接受',
                                value: _api.InvitationStatus.ACCEPTED
                            },
                            {
                                text: '已拒绝',
                                value: _api.InvitationStatus.REJECTED
                            },
                            {
                                text: '已过期',
                                value: _api.InvitationStatus.EXPIRED
                            },
                            {
                                text: '已取消',
                                value: _api.InvitationStatus.CANCELLED
                            }
                        ],
                        onFilter: (value, record)=>record.status === value
                    },
                    {
                        title: '邀请时间',
                        dataIndex: 'invitedAt',
                        key: 'invitedAt',
                        render: (time)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 370,
                                columnNumber: 9
                            }, this),
                        sorter: (a, b)=>(0, _dayjs.default)(a.invitedAt).unix() - (0, _dayjs.default)(b.invitedAt).unix()
                    },
                    {
                        title: '过期时间',
                        dataIndex: 'expiresAt',
                        key: 'expiresAt',
                        render: (time, record)=>{
                            const isExpired = record.isExpired;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                title: (0, _dayjs.default)(time).format('YYYY-MM-DD HH:mm:ss'),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: isExpired ? 'danger' : 'secondary',
                                    children: (0, _dayjs.default)(time).format('MM-DD HH:mm')
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 384,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 383,
                                columnNumber: 11
                            }, this);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (_, record)=>{
                            if (record.status === _api.InvitationStatus.PENDING && !record.isExpired) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                title: "确定要取消这个邀请吗？",
                                onConfirm: ()=>handleCancelInvitation(record.id),
                                okText: "确定",
                                cancelText: "取消",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    size: "small",
                                    danger: true,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 403,
                                        columnNumber: 49
                                    }, void 0),
                                    children: "取消邀请"
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 403,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 397,
                                columnNumber: 13
                            }, this);
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                type: "secondary",
                                children: "-"
                            }, void 0, false, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 409,
                                columnNumber: 16
                            }, this);
                        }
                    }
                ];
                // 过滤邀请列表
                const filteredInvitations = invitations.filter((invitation)=>{
                    const matchesSearch = !invitationSearchText || invitation.inviteeEmail.toLowerCase().includes(invitationSearchText.toLowerCase()) || invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(invitationSearchText.toLowerCase());
                    const matchesStatus = !statusFilter || invitation.status === statusFilter;
                    return matchesSearch && matchesStatus;
                });
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 431,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: [
                                            "团队成员 (",
                                            (members || []).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 432,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 430,
                                columnNumber: 11
                            }, void 0),
                            style: {
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        style: {
                                            width: '100%',
                                            justifyContent: 'space-between'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "搜索成员姓名或邮箱",
                                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 443,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        value: searchText,
                                                        onChange: (e)=>setSearchText(e.target.value),
                                                        style: {
                                                            width: 250
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 441,
                                                        columnNumber: 15
                                                    }, this),
                                                    selectedRowKeys.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                                                        title: `确定要移除选中的 ${selectedRowKeys.length} 名成员吗？`,
                                                        onConfirm: handleBatchRemove,
                                                        okText: "确定",
                                                        cancelText: "取消",
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            danger: true,
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 455,
                                                                columnNumber: 40
                                                            }, void 0),
                                                            children: [
                                                                "批量移除 (",
                                                                selectedRowKeys.length,
                                                                ")"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 455,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 449,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 440,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "primary",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserAddOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 463,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: ()=>setInviteModalVisible(true),
                                                children: "邀请成员"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 461,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 439,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 438,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: columns,
                                    dataSource: filteredMembers,
                                    rowKey: "id",
                                    loading: loading,
                                    rowSelection: rowSelection,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 名成员`,
                                        pageSize: 10
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 472,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 428,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 491,
                                        columnNumber: 13
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        children: [
                                            "邀请记录 (",
                                            (invitations || []).length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 492,
                                        columnNumber: 13
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 490,
                                columnNumber: 11
                            }, void 0),
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 16
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        style: {
                                            width: '100%',
                                            justifyContent: 'space-between'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                        placeholder: "搜索邮箱或姓名",
                                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                            lineNumber: 502,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        value: invitationSearchText,
                                                        onChange: (e)=>setInvitationSearchText(e.target.value),
                                                        style: {
                                                            width: 200
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 500,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                        placeholder: "筛选状态",
                                                        value: statusFilter,
                                                        onChange: setStatusFilter,
                                                        style: {
                                                            width: 120
                                                        },
                                                        allowClear: true,
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.PENDING,
                                                                children: "待确认"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 514,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.ACCEPTED,
                                                                children: "已接受"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 515,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.REJECTED,
                                                                children: "已拒绝"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 516,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.EXPIRED,
                                                                children: "已过期"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 517,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select.Option, {
                                                                value: _api.InvitationStatus.CANCELLED,
                                                                children: "已取消"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                                lineNumber: 518,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 507,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 499,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 522,
                                                    columnNumber: 21
                                                }, void 0),
                                                onClick: fetchInvitations,
                                                loading: invitationLoading,
                                                children: "刷新"
                                            }, void 0, false, {
                                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                lineNumber: 521,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 498,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 497,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                                    columns: invitationColumns,
                                    dataSource: filteredInvitations,
                                    rowKey: "id",
                                    loading: invitationLoading,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 条邀请记录`,
                                        pageSize: 10
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                    lineNumber: 532,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 488,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "邀请新成员",
                            open: inviteModalVisible,
                            onCancel: ()=>{
                                setInviteModalVisible(false);
                                inviteForm.resetFields();
                            },
                            footer: null,
                            width: 600,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: inviteForm,
                                layout: "vertical",
                                onFinish: handleInviteMembers,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "emails",
                                        label: "邮箱地址",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱地址'
                                            }
                                        ],
                                        extra: "每行一个邮箱地址，支持批量邀请",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 6,
                                            placeholder: "请输入邮箱地址，每行一个 例如： <EMAIL> <EMAIL>"
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 572,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 564,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        name: "message",
                                        label: "邀请消息（可选）",
                                        extra: "您可以添加一些邀请消息，让被邀请人更好地了解邀请意图",
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                            rows: 3,
                                            placeholder: "欢迎加入我们的团队！我们期待与您一起工作...",
                                            maxLength: 500,
                                            showCount: true
                                        }, void 0, false, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 582,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 577,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                        lineNumber: 591,
                                                        columnNumber: 62
                                                    }, void 0),
                                                    children: "发送邀请"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 591,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: ()=>setInviteModalVisible(false),
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                                    lineNumber: 594,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                            lineNumber: 590,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                        lineNumber: 589,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                                lineNumber: 559,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                            lineNumber: 549,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/team-management/components/TeamMemberManagement.tsx",
                    lineNumber: 426,
                    columnNumber: 5
                }, this);
            };
            _s(TeamMemberManagement, "clJXlt+1N7F8WykBT8WZbIbsLSc=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TeamMemberManagement;
            var _default = TeamMemberManagement;
            var _c;
            $RefreshReg$(_c, "TeamMemberManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '12614343781663423712';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ],
        "src/utils/testErrorHandling.ts": [
            "src/utils/testErrorHandling.ts"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_team-management_index_tsx-async.3541629869778782604.hot-update.js.map