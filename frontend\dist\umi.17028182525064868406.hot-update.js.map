{"version": 3, "sources": ["umi.17028182525064868406.hot-update.js", "src/services/team.ts", "src/utils/errorHandler.ts", "src/constants/responseCodes.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='3936984386682665707';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport type {\n  CreateTeamRequest,\n  InviteMembersRequest,\n  PageRequest,\n  PageResponse,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  UpdateTeamRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 团队服务类\n *\n * 提供团队相关的所有API接口，包括：\n * - 团队创建和管理\n * - 团队成员管理\n * - 团队邀请功能\n * - 团队统计信息\n *\n * 权限说明：\n * - Account Token：用户级别操作（创建团队、获取用户团队列表）\n * - Team Token：团队级别操作（团队详情、成员管理等）\n * - 创建者权限：某些操作仅团队创建者可执行\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class TeamService {\n  /**\n   * 创建团队\n   *\n   * 创建新的团队，创建者自动成为团队管理员。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @param data 团队创建请求参数\n   * @param data.name 团队名称（必填，2-50字符）\n   * @param data.description 团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 创建的团队信息\n   * @throws 当团队名称重复或用户权限不足时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const newTeam = await TeamService.createTeam({\n   *   name: '开发团队',\n   *   description: '负责产品开发的团队'\n   * });\n   * console.log('团队创建成功:', newTeam.name);\n   * ```\n   */\n  static async createTeam(\n    data: CreateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表\n   *\n   * 获取当前用户所属的所有团队的基本信息。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @returns Promise<TeamDetailResponse[]> 团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeams();\n   * console.log('用户所属团队数量:', teams.length);\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, ID: ${team.id}`);\n   * });\n   * ```\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   *\n   * 获取当前用户所属的所有团队，包含每个团队的统计信息。\n   * 用于个人中心的团队列表展示。\n   *\n   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeamsWithStats();\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);\n   * });\n   * ```\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>(\n      '/teams?includeStats=true',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情\n   *\n   * 获取当前选择团队的详细信息。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @returns Promise<TeamDetailResponse> 团队详细信息\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teamDetail = await TeamService.getCurrentTeamDetail();\n   * console.log('当前团队:', teamDetail.name);\n   * console.log('团队描述:', teamDetail.description);\n   * console.log('成员数量:', teamDetail.memberCount);\n   * ```\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息\n   *\n   * 更新当前团队的基本信息，如名称、描述等。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 团队更新请求参数\n   * @param data.name 新的团队名称（可选）\n   * @param data.description 新的团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 更新后的团队信息\n   * @throws 当用户非团队创建者或团队名称重复时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const updatedTeam = await TeamService.updateCurrentTeam({\n   *   name: '新团队名称',\n   *   description: '更新后的团队描述'\n   * });\n   * console.log('团队信息更新成功');\n   * ```\n   */\n  static async updateCurrentTeam(\n    data: UpdateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>(\n      '/teams/current',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 获取当前团队成员列表（简单数组格式）\n   *\n   * 获取当前团队的所有成员，返回简单数组格式。\n   * 内部调用分页接口并获取所有成员。\n   *\n   * @returns Promise<TeamMemberResponse[]> 团队成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const members = await TeamService.getCurrentTeamMembers();\n   * console.log('团队成员数量:', members.length);\n   * members.forEach(member => {\n   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);\n   * });\n   * ```\n   */\n  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {\n    const response = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000, // 获取大量数据以确保包含所有成员\n    });\n    return response?.list || [];\n  }\n\n  /**\n   * 获取当前团队成员列表（分页格式）\n   *\n   * 获取当前团队的成员列表，支持分页查询。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @param params 分页查询参数（可选）\n   * @param params.current 当前页码（默认1）\n   * @param params.pageSize 每页大小（默认10）\n   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const membersPage = await TeamService.getTeamMembers({\n   *   current: 1,\n   *   pageSize: 20\n   * });\n   *\n   * console.log('总成员数:', membersPage.total);\n   * console.log('当前页成员:', membersPage.list);\n   * ```\n   */\n  static async getTeamMembers(\n    params?: PageRequest,\n  ): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params,\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员\n   *\n   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 邀请请求参数\n   * @param data.emails 被邀请人的邮箱列表\n   * @returns Promise<void> 邀请发送成功时resolve\n   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.inviteMembers({\n   *   emails: ['<EMAIL>', '<EMAIL>']\n   * });\n   * console.log('邀请已发送');\n   * ```\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>(\n      '/teams/current/members/invite',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员\n   *\n   * 从当前团队中移除指定成员。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 要移除的成员ID\n   * @returns Promise<void> 移除成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.removeMember(123);\n   * console.log('成员已移除');\n   * ```\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(\n      `/teams/current/members/${memberId}`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 更新团队成员状态\n   *\n   * 更新团队成员的激活状态（启用/禁用）。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 成员ID\n   * @param isActive 是否激活（true=启用，false=禁用）\n   * @returns Promise<void> 更新成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * // 禁用成员\n   * await TeamService.updateMemberStatus(123, false);\n   * console.log('成员已禁用');\n   *\n   * // 启用成员\n   * await TeamService.updateMemberStatus(123, true);\n   * console.log('成员已启用');\n   * ```\n   */\n  static async updateMemberStatus(memberId: number, isActive: boolean): Promise<void> {\n    const response = await apiRequest.put<void>(\n      `/teams/current/members/${memberId}/status?isActive=${isActive}`,\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 获取团队统计信息\n   *\n   * 获取当前团队的统计信息，包括成员数量、活跃成员数等。\n   * 注意：当前通过团队详情和成员列表计算，等待后端提供专门的统计接口。\n   *\n   * @returns Promise<object> 团队统计信息\n   * @returns Promise<object>.memberCount 总成员数\n   * @returns Promise<object>.activeMembers 活跃成员数（状态为启用的成员）\n   * @returns Promise<object>.recentActivity 最近活跃成员数（7天内有访问的成员）\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const stats = await TeamService.getTeamStats();\n   * console.log('总成员数:', stats.memberCount);\n   * console.log('活跃成员数:', stats.activeMembers);\n   * console.log('最近活跃成员数:', stats.recentActivity);\n   * ```\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await TeamService.getCurrentTeamDetail();\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    const activeMembers = members.list.filter(\n      (member) => member.isActive,\n    ).length;\n    const recentActivity = members.list.filter((member) => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n\n}\n\n// 导出默认实例\nexport default TeamService;\n", "/**\n * 集中式错误消息处理工具\n * \n * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。\n * 支持message和notification组件，确保错误消息显示的一致性。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { ResponseCode, isError, getDescription } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\nimport { AuthService } from '@/services';\n\n// ============= 错误显示类型 =============\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 'silent',\n  /** 使用message.warning显示 */\n  WARNING = 'warning',\n  /** 使用message.error显示 */\n  ERROR = 'error',\n  /** 使用notification显示 */\n  NOTIFICATION = 'notification',\n  /** 重定向到登录页 */\n  REDIRECT = 'redirect',\n}\n\n// ============= 错误处理配置 =============\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否显示详细错误信息 */\n  showDetails?: boolean;\n  /** 错误回调函数 */\n  onError?: (error: any) => void;\n}\n\n/**\n * 默认错误处理配置\n */\nconst DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {\n  [ResponseCode.BAD_REQUEST]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.UNAUTHORIZED]: {\n    displayType: ErrorDisplayType.REDIRECT,\n  },\n  [ResponseCode.FORBIDDEN]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.NOT_FOUND]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.CONFLICT]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.UNPROCESSABLE_ENTITY]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.TOO_MANY_REQUESTS]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.INTERNAL_SERVER_ERROR]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务器内部错误，请稍后重试',\n  },\n  [ResponseCode.BAD_GATEWAY]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '网关错误，请稍后重试',\n  },\n  [ResponseCode.SERVICE_UNAVAILABLE]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务暂时不可用，请稍后重试',\n  },\n  [ResponseCode.GATEWAY_TIMEOUT]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '请求超时，请稍后重试',\n  },\n};\n\n// ============= 核心错误处理方法 =============\n\n/**\n * 处理API响应错误\n * \n * @param response API响应对象\n * @param config 错误处理配置\n */\nexport const handleApiError = (\n  response: ApiResponse<any>,\n  config?: ErrorHandlerConfig\n): void => {\n  if (!response || ResponseCode.isSuccess(response.code)) {\n    return;\n  }\n\n  const errorCode = response.code;\n  const errorMessage = response.message || getDescription(errorCode);\n\n  // 调试日志\n  console.log('handleApiError 被调用:', {\n    errorCode,\n    errorMessage,\n    response,\n    config\n  });\n\n  // 合并配置\n  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {\n    displayType: ErrorDisplayType.ERROR,\n  };\n  const finalConfig = { ...defaultConfig, ...config };\n\n  // 使用自定义消息或响应消息\n  const displayMessage = finalConfig.customMessage || errorMessage;\n\n  console.log('错误处理配置:', {\n    defaultConfig,\n    finalConfig,\n    displayMessage\n  });\n\n  // 执行错误回调\n  if (finalConfig.onError) {\n    finalConfig.onError({ code: errorCode, message: errorMessage, response });\n  }\n\n  // 根据显示类型处理错误\n  switch (finalConfig.displayType) {\n    case ErrorDisplayType.SILENT:\n      console.log('静默处理错误');\n      break;\n\n    case ErrorDisplayType.WARNING:\n      console.log('显示警告消息:', displayMessage);\n      message.warning(displayMessage);\n      break;\n\n    case ErrorDisplayType.ERROR:\n      console.log('显示错误消息:', displayMessage);\n      message.error(displayMessage);\n      break;\n\n    case ErrorDisplayType.NOTIFICATION:\n      console.log('显示通知:', displayMessage);\n      notification.error({\n        message: '操作失败',\n        description: displayMessage,\n        duration: 4.5,\n      });\n      break;\n\n    case ErrorDisplayType.REDIRECT:\n      console.log('处理认证错误:', errorCode, displayMessage);\n      handleAuthError(errorCode, displayMessage);\n      break;\n\n    default:\n      console.log('默认错误处理:', displayMessage);\n      message.error(displayMessage);\n      break;\n  }\n};\n\n/**\n * 处理认证相关错误\n * \n * @param errorCode 错误代码\n * @param errorMessage 错误消息\n */\nconst handleAuthError = (errorCode: number, errorMessage: string): void => {\n  console.log('handleAuthError 被调用:', { errorCode, errorMessage });\n\n  if (errorCode === ResponseCode.UNAUTHORIZED) {\n    // 检查当前路径，避免在某些页面立即跳转\n    const currentPath = window.location.pathname;\n    const isDashboardRelated =\n      currentPath.startsWith('/dashboard') ||\n      currentPath.startsWith('/team');\n\n    console.log('401错误处理:', { currentPath, isDashboardRelated });\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      return;\n    }\n\n    // 清除认证信息并跳转到登录页\n    AuthService.clearTokens();\n    console.log('显示401错误消息并跳转登录页:', errorMessage);\n    message.error(errorMessage || '登录已过期，请重新登录');\n    history.push('/user/login');\n  } else if (errorCode === ResponseCode.FORBIDDEN) {\n    console.log('显示403错误消息:', errorMessage);\n    message.error(errorMessage || '没有权限访问该资源');\n  }\n};\n\n// ============= 便捷方法 =============\n\n/**\n * 显示成功消息\n * \n * @param message 成功消息\n */\nexport const showSuccess = (msg: string): void => {\n  message.success(msg);\n};\n\n/**\n * 显示警告消息\n * \n * @param message 警告消息\n */\nexport const showWarning = (msg: string): void => {\n  message.warning(msg);\n};\n\n/**\n * 显示错误消息\n * \n * @param message 错误消息\n */\nexport const showError = (msg: string): void => {\n  message.error(msg);\n};\n\n/**\n * 显示信息消息\n * \n * @param message 信息消息\n */\nexport const showInfo = (msg: string): void => {\n  message.info(msg);\n};\n\n/**\n * 显示通知\n * \n * @param title 通知标题\n * @param description 通知描述\n * @param type 通知类型\n */\nexport const showNotification = (\n  title: string,\n  description: string,\n  type: 'success' | 'info' | 'warning' | 'error' = 'info'\n): void => {\n  notification[type]({\n    message: title,\n    description,\n    duration: 4.5,\n  });\n};\n\n// ============= 错误处理Hook =============\n\n/**\n * 创建错误处理器\n * \n * @param config 默认错误处理配置\n * @returns 错误处理函数\n */\nexport const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {\n  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {\n    const finalConfig = { ...defaultConfig, ...config };\n    handleApiError(response, finalConfig);\n  };\n};\n\n// ============= 默认导出 =============\n\nexport default {\n  handleApiError,\n  showSuccess,\n  showWarning,\n  showError,\n  showInfo,\n  showNotification,\n  createErrorHandler,\n  ErrorDisplayType,\n};\n", "/**\n * 标准化响应代码常量\n * \n * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。\n * 所有API响应处理都应该使用这些预定义的响应代码。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\n// ============= 成功状态码 =============\n\n/**\n * 操作成功\n */\nexport const SUCCESS = 200;\n\n// ============= 客户端错误状态码 =============\n\n/**\n * 请求参数错误或业务逻辑错误\n */\nexport const BAD_REQUEST = 400;\n\n/**\n * 未认证，需要登录\n */\nexport const UNAUTHORIZED = 401;\n\n/**\n * 权限不足，已认证但无权限访问\n */\nexport const FORBIDDEN = 403;\n\n/**\n * 资源不存在\n */\nexport const NOT_FOUND = 404;\n\n/**\n * 资源冲突（如重复创建、数据冲突等）\n */\nexport const CONFLICT = 409;\n\n/**\n * 请求格式正确但语义错误（如验证失败）\n */\nexport const UNPROCESSABLE_ENTITY = 422;\n\n/**\n * 请求频率限制\n */\nexport const TOO_MANY_REQUESTS = 429;\n\n// ============= 服务器错误状态码 =============\n\n/**\n * 服务器内部错误\n */\nexport const INTERNAL_SERVER_ERROR = 500;\n\n/**\n * 网关错误\n */\nexport const BAD_GATEWAY = 502;\n\n/**\n * 服务不可用\n */\nexport const SERVICE_UNAVAILABLE = 503;\n\n/**\n * 网关超时\n */\nexport const GATEWAY_TIMEOUT = 504;\n\n// ============= 响应代码分类方法 =============\n\n/**\n * 判断是否为成功响应代码\n * \n * @param code 响应代码\n * @returns 是否为成功代码\n */\nexport const isSuccess = (code: number): boolean => {\n  return code === SUCCESS;\n};\n\n/**\n * 判断是否为客户端错误代码\n * \n * @param code 响应代码\n * @returns 是否为客户端错误代码\n */\nexport const isClientError = (code: number): boolean => {\n  return code >= 400 && code < 500;\n};\n\n/**\n * 判断是否为服务器错误代码\n * \n * @param code 响应代码\n * @returns 是否为服务器错误代码\n */\nexport const isServerError = (code: number): boolean => {\n  return code >= 500 && code < 600;\n};\n\n/**\n * 判断是否为错误代码（非成功代码）\n * \n * @param code 响应代码\n * @returns 是否为错误代码\n */\nexport const isError = (code: number): boolean => {\n  return !isSuccess(code);\n};\n\n// ============= 响应代码描述方法 =============\n\n/**\n * 获取响应代码的默认描述\n * \n * @param code 响应代码\n * @returns 响应代码描述\n */\nexport const getDescription = (code: number): string => {\n  switch (code) {\n    case SUCCESS:\n      return '操作成功';\n    case BAD_REQUEST:\n      return '请求参数错误';\n    case UNAUTHORIZED:\n      return '未认证，需要登录';\n    case FORBIDDEN:\n      return '权限不足';\n    case NOT_FOUND:\n      return '资源不存在';\n    case CONFLICT:\n      return '资源冲突';\n    case UNPROCESSABLE_ENTITY:\n      return '请求语义错误';\n    case TOO_MANY_REQUESTS:\n      return '请求频率过高';\n    case INTERNAL_SERVER_ERROR:\n      return '服务器内部错误';\n    case BAD_GATEWAY:\n      return '网关错误';\n    case SERVICE_UNAVAILABLE:\n      return '服务不可用';\n    case GATEWAY_TIMEOUT:\n      return '网关超时';\n    default:\n      return '未知错误';\n  }\n};\n\n// ============= 响应代码集合 =============\n\n/**\n * 所有成功响应代码\n */\nexport const SUCCESS_CODES = [SUCCESS];\n\n/**\n * 所有客户端错误响应代码\n */\nexport const CLIENT_ERROR_CODES = [\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n];\n\n/**\n * 所有服务器错误响应代码\n */\nexport const SERVER_ERROR_CODES = [\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n];\n\n/**\n * 所有错误响应代码\n */\nexport const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];\n\n/**\n * 所有响应代码\n */\nexport const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];\n\n// ============= 默认导出 =============\n\n/**\n * 响应代码常量对象\n */\nexport const ResponseCode = {\n  // 成功状态码\n  SUCCESS,\n  \n  // 客户端错误状态码\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n  \n  // 服务器错误状态码\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n  \n  // 工具方法\n  isSuccess,\n  isClientError,\n  isServerError,\n  isError,\n  getDescription,\n  \n  // 代码集合\n  SUCCESS_CODES,\n  CLIENT_ERROR_CODES,\n  SERVER_ERROR_CODES,\n  ERROR_CODES,\n  ALL_CODES,\n} as const;\n\nexport default ResponseCode;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC+BA,WAAW;2BAAX;;gBA4Vb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CAlX2B;kDACE;iDACE;;;;;;;;;YAmBxB,MAAM;gBACX;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,aAAa,WACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBAErE,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,aAAa,kBACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,YAAY,cAAc,CAAC;wBAChD,SAAS;wBACT,UAAU;oBACZ;oBACA,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;gBAC7B;gBAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,eACX,MAAoB,EACuB;oBAC3C,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,iCACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;GAeC,GACD,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CACtC,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBAEtC,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,aAAa,mBAAmB,QAAgB,EAAE,QAAiB,EAAiB;oBAClF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,uBAAuB,EAAE,SAAS,iBAAiB,EAAE,SAAS,CAAC;oBAElE,OAAO,SAAS,IAAI;gBACtB;gBAIA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,aAAa,eAIV;oBACD,oBAAoB;oBACpB,mBAAmB;oBACnB,MAAM,aAAa,MAAM,YAAY,oBAAoB;oBACzD,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAEA,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CACvC,CAAC,SAAW,OAAO,QAAQ,EAC3B,MAAM;oBACR,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1C,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;YAGF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC3GF,kBAAkB;2BAAlB;;gBAOb,mCAAmC;gBAEnC,OASE;2BATF;;gBAxLa,cAAc;2BAAd;;gBAuIA,SAAS;2BAAT;;gBASA,QAAQ;2BAAR;;gBAWA,gBAAgB;2BAAhB;;gBAtCA,WAAW;2BAAX;;gBASA,WAAW;2BAAX;;;;;yCAzNyB;wCACd;kDAC8B;6CAE1B;;;;;;;;;;sBAOhB;gBACV,iBAAiB;gBAEjB,wBAAwB;gBAExB,sBAAsB;gBAEtB,qBAAqB;gBAErB,YAAY;eATF,qBAAA;YA6BZ;;CAEC,GACD,MAAM,uBAA2D;gBAC/D,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,YAAY,CAAC,EAAE;oBAC3B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,QAAQ,CAAC,EAAE;oBACvB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,oBAAoB,CAAC,EAAE;oBACnC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,iBAAiB,CAAC,EAAE;oBAChC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,qBAAqB,CAAC,EAAE;oBACpC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,mBAAmB,CAAC,EAAE;oBAClC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,eAAe,CAAC,EAAE;oBAC9B,WAAW;oBACX,eAAe;gBACjB;YACF;YAUO,MAAM,iBAAiB,CAC5B,UACA;gBAEA,IAAI,CAAC,YAAY,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACnD;gBAGF,MAAM,YAAY,SAAS,IAAI;gBAC/B,MAAM,eAAe,SAAS,OAAO,IAAI,IAAA,6BAAc,EAAC;gBAExD,OAAO;gBACP,QAAQ,GAAG,CAAC,uBAAuB;oBACjC;oBACA;oBACA;oBACA;gBACF;gBAEA,OAAO;gBACP,MAAM,gBAAgB,oBAAoB,CAAC,UAAU,IAAI;oBACvD,WAAW;gBACb;gBACA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,eAAe;gBACf,MAAM,iBAAiB,YAAY,aAAa,IAAI;gBAEpD,QAAQ,GAAG,CAAC,WAAW;oBACrB;oBACA;oBACA;gBACF;gBAEA,SAAS;gBACT,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC;oBAAE,MAAM;oBAAW,SAAS;oBAAc;gBAAS;gBAGzE,aAAa;gBACb,OAAQ,YAAY,WAAW;oBAC7B;wBACE,QAAQ,GAAG,CAAC;wBACZ;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,OAAO,CAAC;wBAChB;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;oBAEF;wBACE,QAAQ,GAAG,CAAC,SAAS;wBACrB,kBAAY,CAAC,KAAK,CAAC;4BACjB,SAAS;4BACT,aAAa;4BACb,UAAU;wBACZ;wBACA;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW,WAAW;wBAClC,gBAAgB,WAAW;wBAC3B;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;gBACJ;YACF;YAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,WAAmB;gBAC1C,QAAQ,GAAG,CAAC,wBAAwB;oBAAE;oBAAW;gBAAa;gBAE9D,IAAI,cAAc,2BAAY,CAAC,YAAY,EAAE;oBAC3C,qBAAqB;oBACrB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oBAEzB,QAAQ,GAAG,CAAC,YAAY;wBAAE;wBAAa;oBAAmB;oBAE1D,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb;oBACF;oBAEA,gBAAgB;oBAChB,qBAAW,CAAC,WAAW;oBACvB,QAAQ,GAAG,CAAC,oBAAoB;oBAChC,aAAO,CAAC,KAAK,CAAC,gBAAgB;oBAC9B,YAAO,CAAC,IAAI,CAAC;gBACf,OAAO,IAAI,cAAc,2BAAY,CAAC,SAAS,EAAE;oBAC/C,QAAQ,GAAG,CAAC,cAAc;oBAC1B,aAAO,CAAC,KAAK,CAAC,gBAAgB;gBAChC;YACF;YASO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,YAAY,CAAC;gBACxB,aAAO,CAAC,KAAK,CAAC;YAChB;YAOO,MAAM,WAAW,CAAC;gBACvB,aAAO,CAAC,IAAI,CAAC;YACf;YASO,MAAM,mBAAmB,CAC9B,OACA,aACA,OAAiD,MAAM;gBAEvD,kBAAY,CAAC,KAAK,CAAC;oBACjB,SAAS;oBACT;oBACA,UAAU;gBACZ;YACF;YAUO,MAAM,qBAAqB,CAAC;gBACjC,OAAO,CAAC,UAA4B;oBAClC,MAAM,cAAc;wBAAE,GAAG,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAClD,eAAe,UAAU;gBAC3B;YACF;gBAIA,WAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCnGa,SAAS;2BAAT;;gBAnIA,WAAW;2BAAX;;gBA1CA,WAAW;2BAAX;;gBAiJA,kBAAkB;2BAAlB;;gBA7HA,QAAQ;2BAAR;;gBAoJA,WAAW;2BAAX;;gBA9JA,SAAS;2BAAT;;gBA0CA,eAAe;2BAAf;;gBAfA,qBAAqB;2BAArB;;gBAtBA,SAAS;2BAAT;;gBAqKA,YAAY;2BAAZ;;gBAtBA,kBAAkB;2BAAlB;;gBA/GA,mBAAmB;2BAAnB;;gBAtDA,OAAO;2BAAP;;gBAmJA,aAAa;2BAAb;;gBA9GA,iBAAiB;2BAAjB;;gBAzBA,YAAY;2BAAZ;;gBAoBA,oBAAoB;2BAApB;;gBA6Lb,OAA4B;2BAA5B;;gBA9Ga,cAAc;2BAAd;;gBAhCA,aAAa;2BAAb;;gBAoBA,OAAO;2BAAP;;gBAVA,aAAa;2BAAb;;gBApBA,SAAS;2BAAT;;;;;;;;;;;;;YArEN,MAAM,UAAU;YAOhB,MAAM,cAAc;YAKpB,MAAM,eAAe;YAKrB,MAAM,YAAY;YAKlB,MAAM,YAAY;YAKlB,MAAM,WAAW;YAKjB,MAAM,uBAAuB;YAK7B,MAAM,oBAAoB;YAO1B,MAAM,wBAAwB;YAK9B,MAAM,cAAc;YAKpB,MAAM,sBAAsB;YAK5B,MAAM,kBAAkB;YAUxB,MAAM,YAAY,CAAC;gBACxB,OAAO,SAAS;YAClB;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,UAAU,CAAC;gBACtB,OAAO,CAAC,UAAU;YACpB;YAUO,MAAM,iBAAiB,CAAC;gBAC7B,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE,OAAO;gBACX;YACF;YAOO,MAAM,gBAAgB;gBAAC;aAAQ;YAK/B,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,cAAc;mBAAI;mBAAuB;aAAmB;YAKlE,MAAM,YAAY;mBAAI;mBAAkB;aAAY;YAOpD,MAAM,eAAe;gBAC1B,QAAQ;gBACR;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IHzOD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}