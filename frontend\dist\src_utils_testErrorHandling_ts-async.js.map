{"version": 3, "sources": ["src/utils/testErrorHandling.ts"], "sourcesContent": ["/**\n * 错误处理测试工具\n * \n * 用于测试和验证错误处理机制是否正常工作\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { handleApiError } from './errorHandler';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\n\n/**\n * 测试403错误处理\n */\nexport const test403Error = () => {\n  const mockResponse: ApiResponse<any> = {\n    code: ResponseCode.FORBIDDEN,\n    message: '您的账户已在此团队中被停用',\n    data: null,\n    timestamp: new Date().toISOString(),\n  };\n\n  console.log('测试403错误处理:', mockResponse);\n  handleApiError(mockResponse);\n};\n\n/**\n * 测试各种错误代码\n */\nexport const testAllErrorCodes = () => {\n  const testCases = [\n    {\n      code: ResponseCode.BAD_REQUEST,\n      message: '请求参数错误',\n    },\n    {\n      code: ResponseCode.UNAUTHORIZED,\n      message: '登录已过期，请重新登录',\n    },\n    {\n      code: ResponseCode.FORBIDDEN,\n      message: '您的账户已在此团队中被停用',\n    },\n    {\n      code: ResponseCode.NOT_FOUND,\n      message: '团队不存在',\n    },\n    {\n      code: ResponseCode.CONFLICT,\n      message: '邮箱已被注册',\n    },\n    {\n      code: ResponseCode.UNPROCESSABLE_ENTITY,\n      message: '验证码错误',\n    },\n    {\n      code: ResponseCode.TOO_MANY_REQUESTS,\n      message: '请求频率过高，请60秒后重试',\n    },\n    {\n      code: ResponseCode.INTERNAL_SERVER_ERROR,\n      message: '数据库连接失败',\n    },\n  ];\n\n  testCases.forEach((testCase, index) => {\n    setTimeout(() => {\n      const mockResponse: ApiResponse<any> = {\n        code: testCase.code,\n        message: testCase.message,\n        data: null,\n        timestamp: new Date().toISOString(),\n      };\n\n      console.log(`测试错误代码 ${testCase.code}:`, mockResponse);\n      handleApiError(mockResponse);\n    }, index * 1000); // 每秒显示一个错误\n  });\n};\n\n/**\n * 在浏览器控制台中暴露测试函数\n */\nif (typeof window !== 'undefined') {\n  (window as any).testErrorHandling = {\n    test403Error,\n    testAllErrorCodes,\n  };\n  \n  console.log('错误处理测试工具已加载，可以在控制台中使用：');\n  console.log('- window.testErrorHandling.test403Error() // 测试403错误');\n  console.log('- window.testErrorHandling.testAllErrorCodes() // 测试所有错误代码');\n}\n"], "names": [], "mappings": ";;;AAAA;;;;;;;CAOC;;;;;;;;;;;IASY,YAAY;eAAZ;;IAeA,iBAAiB;eAAjB;;;;;qCAtBkB;sCACF;;;;;;;;;AAMtB,MAAM,eAAe;IAC1B,MAAM,eAAiC;QACrC,MAAM,2BAAY,CAAC,SAAS;QAC5B,SAAS;QACT,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,QAAQ,GAAG,CAAC,cAAc;IAC1B,IAAA,4BAAc,EAAC;AACjB;AAKO,MAAM,oBAAoB;IAC/B,MAAM,YAAY;QAChB;YACE,MAAM,2BAAY,CAAC,WAAW;YAC9B,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,YAAY;YAC/B,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,SAAS;YAC5B,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,SAAS;YAC5B,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,QAAQ;YAC3B,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,oBAAoB;YACvC,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,iBAAiB;YACpC,SAAS;QACX;QACA;YACE,MAAM,2BAAY,CAAC,qBAAqB;YACxC,SAAS;QACX;KACD;IAED,UAAU,OAAO,CAAC,CAAC,UAAU;QAC3B,WAAW;YACT,MAAM,eAAiC;gBACrC,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,OAAO;gBACzB,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE;YACxC,IAAA,4BAAc,EAAC;QACjB,GAAG,QAAQ,OAAO,WAAW;IAC/B;AACF;AAEA;;CAEC,GACD,IAAI,OAAO,WAAW,aAAa;IAChC,OAAe,iBAAiB,GAAG;QAClC;QACA;IACF;IAEA,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;AACd"}