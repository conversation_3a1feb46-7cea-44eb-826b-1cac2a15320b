{"version": 3, "sources": ["umi.7987072585202845008.hot-update.js", "src/services/auth.ts", "src/utils/errorHandler.ts", "src/constants/responseCodes.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='1779647971134067934';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/utils/testErrorHandling.ts\":[\"src/utils/testErrorHandling.ts\"]});;\r\n  },\r\n);\r\n", "/**\n * 认证相关 API 服务\n */\n\nimport type {\n  LoginRequest,\n  LoginResponse,\n  SendVerificationCodeRequest,\n  SendVerificationCodeResponse\n} from '@/types/api';\nimport { apiRequest, safeApiRequest, TokenManager } from '@/utils/request';\nimport { ResponseCode } from '@/constants/responseCodes';\nimport { handleApiError } from '@/utils/errorHandler';\n\n/**\n * 认证服务类（验证码登录系统）\n *\n * 提供基于验证码的用户认证系统，支持：\n * - 验证码发送和验证\n * - 用户登录（自动注册新用户）\n * - 团队选择和切换\n * - Token管理和刷新\n * - 用户登出和会话管理\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class AuthService {\n  /**\n   * 发送验证码\n   *\n   * 向指定邮箱发送6位数字验证码，用于登录验证。\n   * 如果邮箱未注册，系统会在登录时自动创建新用户。\n   *\n   * @param data 验证码发送请求参数\n   * @param data.email 接收验证码的邮箱地址\n   * @returns Promise<SendVerificationCodeResponse> 发送结果，包含成功状态和调试信息\n   * @throws 当邮箱格式错误或发送频率过高时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const response = await AuthService.sendVerificationCode({\n   *   email: '<EMAIL>'\n   * });\n   *\n   * if (response.success) {\n   *   console.log('验证码发送成功');\n   *   // 在开发环境中，验证码会自动填充到输入框\n   * }\n   * ```\n   */\n  static async sendVerificationCode(data: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> {\n    const response = await apiRequest.post<SendVerificationCodeResponse>('/auth/send-code', data);\n\n    // Check response code and handle errors\n    if (!ResponseCode.isSuccess(response.code)) {\n      handleApiError(response);\n      throw new Error(response.message);\n    }\n\n    // 在开发环境中输出验证码到控制台（用于调试）\n    if (process.env.NODE_ENV === 'development' && response.data.success) {\n      if (response.data.debugCode) {\n        console.log('验证码:', response.data.debugCode);\n\n        // 提供快速填充功能\n        console.log('快速填充验证码: window.devHelper?.quickFillVerificationCode(\"' + response.data.debugCode + '\")');\n\n        // 自动填充验证码（延迟1秒，确保用户能看到控制台输出）\n        setTimeout(() => {\n          const codeInput = document.querySelector('input[placeholder*=\"验证码\"]') as HTMLInputElement;\n          if (codeInput) {\n            codeInput.value = response.data.debugCode!;\n            codeInput.dispatchEvent(new Event('input', { bubbles: true }));\n            codeInput.dispatchEvent(new Event('change', { bubbles: true }));\n            console.log('✅ 验证码已自动填充到输入框');\n          }\n        }, 1000);\n      } else {\n        console.log('验证码发送成功，请查看后端日志获取验证码');\n      }\n    }\n\n    return response.data;\n  }\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  /**\n   * 用户登录（验证码登录）\n   *\n   * 使用邮箱和验证码进行登录。如果邮箱未注册，系统会自动创建新用户。\n   * 登录成功后返回用户信息和可选择的团队列表。\n   *\n   * @param data 登录请求参数\n   * @param data.email 用户邮箱\n   * @param data.code 6位数字验证码\n   * @returns Promise<LoginResponse> 登录响应，包含用户信息、Token和团队列表\n   * @throws 当验证码错误、已过期或邮箱格式错误时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const loginResponse = await AuthService.login({\n   *   email: '<EMAIL>',\n   *   code: '123456'\n   * });\n   *\n   * console.log('用户信息:', loginResponse.user);\n   * console.log('可选团队:', loginResponse.teams);\n   *\n   * if (loginResponse.teams.length > 0) {\n   *   // 需要选择团队\n   *   await AuthService.selectTeam({ teamId: loginResponse.teams[0].id });\n   * }\n   * ```\n   */\n  static async login(data: LoginRequest): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>('/auth/login', data);\n\n    // 保存用户Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 刷新Token\n   *\n   * 使用当前有效的Token获取新的Token，延长会话时间。\n   * 通常在Token即将过期时自动调用。\n   *\n   * @returns Promise<LoginResponse> 包含新Token和用户信息的响应\n   * @throws 当当前Token无效或已过期时抛出异常\n   *\n   * @example\n   * ```typescript\n   * try {\n   *   const refreshResponse = await AuthService.refreshToken();\n   *   console.log('Token已刷新');\n   * } catch (error) {\n   *   console.log('Token刷新失败，需要重新登录');\n   *   // 跳转到登录页面\n   * }\n   * ```\n   */\n  static async refreshToken(): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/refresh-token',\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 用户登出\n   *\n   * 清除服务器端的会话信息并清除本地Token。\n   * 即使服务器请求失败，也会清除本地Token确保用户能够重新登录。\n   *\n   * @returns Promise<void> 登出完成时resolve\n   *\n   * @example\n   * ```typescript\n   * await AuthService.logout();\n   * console.log('已登出');\n   * // 页面会自动跳转到登录页面\n   * ```\n   */\n  static async logout(): Promise<void> {\n    try {\n      await apiRequest.post<void>('/auth/logout');\n    } finally {\n      // 无论请求是否成功，都清除本地 token\n      TokenManager.clearToken();\n    }\n  }\n\n  /**\n   * 验证Token有效性\n   *\n   * 向服务器验证当前Token是否仍然有效。\n   * 用于检查用户会话状态，通常在应用启动时调用。\n   *\n   * @returns Promise<boolean> Token是否有效\n   *\n   * @example\n   * ```typescript\n   * const isValid = await AuthService.validateToken();\n   * if (!isValid) {\n   *   // Token无效，跳转到登录页面\n   *   history.push('/user/login');\n   * }\n   * ```\n   */\n  static async validateToken(): Promise<boolean> {\n    try {\n      const response = await apiRequest.get<boolean>('/auth/validate');\n      return response.data;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 检查是否已登录\n   *\n   * 检查本地是否存储了有效的Token。\n   * 注意：这只检查本地存储，不验证Token的服务器端有效性。\n   *\n   * @returns boolean 是否已登录（本地Token存在）\n   *\n   * @example\n   * ```typescript\n   * if (AuthService.isLoggedIn()) {\n   *   console.log('用户已登录');\n   * } else {\n   *   console.log('用户未登录');\n   * }\n   * ```\n   */\n  static isLoggedIn(): boolean {\n    return TokenManager.hasToken();\n  }\n\n  /**\n   * 获取当前Token\n   *\n   * 从本地存储获取当前用户的Token。\n   *\n   * @returns string | null 当前Token，如果未登录则返回null\n   *\n   * @example\n   * ```typescript\n   * const token = AuthService.getToken();\n   * if (token) {\n   *   console.log('当前Token:', token);\n   * }\n   * ```\n   */\n  static getToken(): string | null {\n    return TokenManager.getToken();\n  }\n\n  /**\n   * 清除Token\n   *\n   * 清除本地存储的Token，但不通知服务器。\n   * 通常用于强制登出或Token过期处理。\n   *\n   * @example\n   * ```typescript\n   * AuthService.clearToken();\n   * console.log('Token已清除');\n   * ```\n   */\n  static clearToken(): void {\n    TokenManager.clearToken();\n  }\n\n  /**\n   * 清除团队Token（兼容性方法）\n   *\n   * 在单Token系统中，清除Token即可。\n   *\n   * @deprecated 使用 clearToken() 替代\n   */\n  static clearTeamToken(): void {\n    // 在单令牌系统中，清除Token即可\n    TokenManager.clearToken();\n  }\n\n  /**\n   * 选择团队\n   *\n   * 在用户登录后选择要操作的团队。\n   * 选择团队后，Token会更新为包含团队上下文的新Token。\n   *\n   * @param data 团队选择请求参数\n   * @param data.teamId 要选择的团队ID\n   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应\n   * @throws 当团队不存在或用户无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teamResponse = await AuthService.selectTeam({\n   *   teamId: 123\n   * });\n   *\n   * console.log('当前团队:', teamResponse.currentTeam);\n   * console.log('团队Token已更新');\n   * ```\n   */\n  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/select-team',\n      data,\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 切换团队\n   *\n   * 在已选择团队的情况下切换到另一个团队。\n   * 与selectTeam功能相同，但语义上表示从一个团队切换到另一个团队。\n   *\n   * @param data 团队切换请求参数\n   * @param data.teamId 要切换到的团队ID\n   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应\n   * @throws 当团队不存在或用户无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const switchResponse = await AuthService.switchTeam({\n   *   teamId: 456\n   * });\n   *\n   * console.log('已切换到团队:', switchResponse.currentTeam?.name);\n   * ```\n   */\n  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/switch-team',\n      data,\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 清除团队上下文\n   *\n   * 清除当前选择的团队，返回到用户级别的Token。\n   * 用户可以重新选择团队或进行用户级别的操作。\n   *\n   * @returns Promise<string> 新的用户级别Token\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const userToken = await AuthService.clearTeam();\n   * console.log('已清除团队上下文，返回用户级别');\n   * ```\n   */\n  static async clearTeam(): Promise<string> {\n    const response = await apiRequest.post<string>('/auth/clear-team');\n\n    // 更新Token\n    if (response.data) {\n      TokenManager.setToken(response.data);\n    }\n\n    return response.data;\n  }\n\n  // ========== 兼容性方法 ==========\n\n  /**\n   * 检查是否已选择团队（兼容性方法）\n   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态\n   */\n  static hasTeamSelected(): boolean {\n    return AuthService.isLoggedIn();\n  }\n  /**\n   * 团队登录（兼容性方法）\n   * @deprecated 使用 selectTeam 替代\n   */\n  static async teamLogin(data: { teamId: number }): Promise<LoginResponse> {\n    return AuthService.selectTeam(data);\n  }\n\n  /**\n   * 清除所有Token（兼容性方法）\n   * @deprecated 使用 clearToken 替代\n   */\n  static clearTokens(): void {\n    AuthService.clearToken();\n  }\n}\n\n// 导出默认实例\nexport default AuthService;\n", "/**\n * 集中式错误消息处理工具\n * \n * 提供统一的错误处理机制，根据响应代码自动显示相应的错误消息。\n * 支持message和notification组件，确保错误消息显示的一致性。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { ResponseCode, isError, getDescription } from '@/constants/responseCodes';\nimport type { ApiResponse } from '@/types/api';\nimport { AuthService } from '@/services';\n\n// ============= 错误显示类型 =============\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 'silent',\n  /** 使用message.warning显示 */\n  WARNING = 'warning',\n  /** 使用message.error显示 */\n  ERROR = 'error',\n  /** 使用notification显示 */\n  NOTIFICATION = 'notification',\n  /** 重定向到登录页 */\n  REDIRECT = 'redirect',\n}\n\n// ============= 错误处理配置 =============\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否显示详细错误信息 */\n  showDetails?: boolean;\n  /** 错误回调函数 */\n  onError?: (error: any) => void;\n}\n\n/**\n * 默认错误处理配置\n */\nconst DEFAULT_ERROR_CONFIG: Record<number, ErrorHandlerConfig> = {\n  [ResponseCode.BAD_REQUEST]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.UNAUTHORIZED]: {\n    displayType: ErrorDisplayType.REDIRECT,\n  },\n  [ResponseCode.FORBIDDEN]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.NOT_FOUND]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.CONFLICT]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.UNPROCESSABLE_ENTITY]: {\n    displayType: ErrorDisplayType.ERROR,\n  },\n  [ResponseCode.TOO_MANY_REQUESTS]: {\n    displayType: ErrorDisplayType.WARNING,\n  },\n  [ResponseCode.INTERNAL_SERVER_ERROR]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务器内部错误，请稍后重试',\n  },\n  [ResponseCode.BAD_GATEWAY]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '网关错误，请稍后重试',\n  },\n  [ResponseCode.SERVICE_UNAVAILABLE]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '服务暂时不可用，请稍后重试',\n  },\n  [ResponseCode.GATEWAY_TIMEOUT]: {\n    displayType: ErrorDisplayType.ERROR,\n    customMessage: '请求超时，请稍后重试',\n  },\n};\n\n// ============= 核心错误处理方法 =============\n\n/**\n * 处理API响应错误\n * \n * @param response API响应对象\n * @param config 错误处理配置\n */\nexport const handleApiError = (\n  response: ApiResponse<any>,\n  config?: ErrorHandlerConfig\n): void => {\n  if (!response || ResponseCode.isSuccess(response.code)) {\n    return;\n  }\n\n  const errorCode = response.code;\n  const errorMessage = response.message || getDescription(errorCode);\n\n  // 调试日志\n  console.log('handleApiError 被调用:', {\n    errorCode,\n    errorMessage,\n    response,\n    config\n  });\n\n  // 合并配置\n  const defaultConfig = DEFAULT_ERROR_CONFIG[errorCode] || {\n    displayType: ErrorDisplayType.ERROR,\n  };\n  const finalConfig = { ...defaultConfig, ...config };\n\n  // 使用自定义消息或响应消息\n  const displayMessage = finalConfig.customMessage || errorMessage;\n\n  console.log('错误处理配置:', {\n    defaultConfig,\n    finalConfig,\n    displayMessage\n  });\n\n  // 执行错误回调\n  if (finalConfig.onError) {\n    finalConfig.onError({ code: errorCode, message: errorMessage, response });\n  }\n\n  // 根据显示类型处理错误\n  switch (finalConfig.displayType) {\n    case ErrorDisplayType.SILENT:\n      console.log('静默处理错误');\n      break;\n\n    case ErrorDisplayType.WARNING:\n      console.log('显示警告消息:', displayMessage);\n      message.warning(displayMessage);\n      break;\n\n    case ErrorDisplayType.ERROR:\n      console.log('显示错误消息:', displayMessage);\n      message.error(displayMessage);\n      break;\n\n    case ErrorDisplayType.NOTIFICATION:\n      console.log('显示通知:', displayMessage);\n      notification.error({\n        message: '操作失败',\n        description: displayMessage,\n        duration: 4.5,\n      });\n      break;\n\n    case ErrorDisplayType.REDIRECT:\n      console.log('处理认证错误:', errorCode, displayMessage);\n      handleAuthError(errorCode, displayMessage);\n      break;\n\n    default:\n      console.log('默认错误处理:', displayMessage);\n      message.error(displayMessage);\n      break;\n  }\n};\n\n/**\n * 处理认证相关错误\n * \n * @param errorCode 错误代码\n * @param errorMessage 错误消息\n */\nconst handleAuthError = (errorCode: number, errorMessage: string): void => {\n  console.log('handleAuthError 被调用:', { errorCode, errorMessage });\n\n  if (errorCode === ResponseCode.UNAUTHORIZED) {\n    // 检查当前路径，避免在某些页面立即跳转\n    const currentPath = window.location.pathname;\n    const isDashboardRelated =\n      currentPath.startsWith('/dashboard') ||\n      currentPath.startsWith('/team');\n\n    console.log('401错误处理:', { currentPath, isDashboardRelated });\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      return;\n    }\n\n    // 清除认证信息并跳转到登录页\n    AuthService.clearTokens();\n    console.log('显示401错误消息并跳转登录页:', errorMessage);\n    message.error(errorMessage || '登录已过期，请重新登录');\n    history.push('/user/login');\n  } else if (errorCode === ResponseCode.FORBIDDEN) {\n    console.log('显示403错误消息:', errorMessage);\n    message.error(errorMessage || '没有权限访问该资源');\n  }\n};\n\n// ============= 便捷方法 =============\n\n/**\n * 显示成功消息\n * \n * @param message 成功消息\n */\nexport const showSuccess = (msg: string): void => {\n  message.success(msg);\n};\n\n/**\n * 显示警告消息\n * \n * @param message 警告消息\n */\nexport const showWarning = (msg: string): void => {\n  message.warning(msg);\n};\n\n/**\n * 显示错误消息\n * \n * @param message 错误消息\n */\nexport const showError = (msg: string): void => {\n  message.error(msg);\n};\n\n/**\n * 显示信息消息\n * \n * @param message 信息消息\n */\nexport const showInfo = (msg: string): void => {\n  message.info(msg);\n};\n\n/**\n * 显示通知\n * \n * @param title 通知标题\n * @param description 通知描述\n * @param type 通知类型\n */\nexport const showNotification = (\n  title: string,\n  description: string,\n  type: 'success' | 'info' | 'warning' | 'error' = 'info'\n): void => {\n  notification[type]({\n    message: title,\n    description,\n    duration: 4.5,\n  });\n};\n\n// ============= 错误处理Hook =============\n\n/**\n * 创建错误处理器\n * \n * @param config 默认错误处理配置\n * @returns 错误处理函数\n */\nexport const createErrorHandler = (defaultConfig?: ErrorHandlerConfig) => {\n  return (response: ApiResponse<any>, config?: ErrorHandlerConfig) => {\n    const finalConfig = { ...defaultConfig, ...config };\n    handleApiError(response, finalConfig);\n  };\n};\n\n// ============= 默认导出 =============\n\nexport default {\n  handleApiError,\n  showSuccess,\n  showWarning,\n  showError,\n  showInfo,\n  showNotification,\n  createErrorHandler,\n  ErrorDisplayType,\n};\n", "/**\n * 标准化响应代码常量\n * \n * 与后端ResponseCode类保持一致，确保前后端响应代码的统一性。\n * 所有API响应处理都应该使用这些预定义的响应代码。\n * \n * <AUTHOR>\n * @since 1.0.0\n */\n\n// ============= 成功状态码 =============\n\n/**\n * 操作成功\n */\nexport const SUCCESS = 200;\n\n// ============= 客户端错误状态码 =============\n\n/**\n * 请求参数错误或业务逻辑错误\n */\nexport const BAD_REQUEST = 400;\n\n/**\n * 未认证，需要登录\n */\nexport const UNAUTHORIZED = 401;\n\n/**\n * 权限不足，已认证但无权限访问\n */\nexport const FORBIDDEN = 403;\n\n/**\n * 资源不存在\n */\nexport const NOT_FOUND = 404;\n\n/**\n * 资源冲突（如重复创建、数据冲突等）\n */\nexport const CONFLICT = 409;\n\n/**\n * 请求格式正确但语义错误（如验证失败）\n */\nexport const UNPROCESSABLE_ENTITY = 422;\n\n/**\n * 请求频率限制\n */\nexport const TOO_MANY_REQUESTS = 429;\n\n// ============= 服务器错误状态码 =============\n\n/**\n * 服务器内部错误\n */\nexport const INTERNAL_SERVER_ERROR = 500;\n\n/**\n * 网关错误\n */\nexport const BAD_GATEWAY = 502;\n\n/**\n * 服务不可用\n */\nexport const SERVICE_UNAVAILABLE = 503;\n\n/**\n * 网关超时\n */\nexport const GATEWAY_TIMEOUT = 504;\n\n// ============= 响应代码分类方法 =============\n\n/**\n * 判断是否为成功响应代码\n * \n * @param code 响应代码\n * @returns 是否为成功代码\n */\nexport const isSuccess = (code: number): boolean => {\n  return code === SUCCESS;\n};\n\n/**\n * 判断是否为客户端错误代码\n * \n * @param code 响应代码\n * @returns 是否为客户端错误代码\n */\nexport const isClientError = (code: number): boolean => {\n  return code >= 400 && code < 500;\n};\n\n/**\n * 判断是否为服务器错误代码\n * \n * @param code 响应代码\n * @returns 是否为服务器错误代码\n */\nexport const isServerError = (code: number): boolean => {\n  return code >= 500 && code < 600;\n};\n\n/**\n * 判断是否为错误代码（非成功代码）\n * \n * @param code 响应代码\n * @returns 是否为错误代码\n */\nexport const isError = (code: number): boolean => {\n  return !isSuccess(code);\n};\n\n// ============= 响应代码描述方法 =============\n\n/**\n * 获取响应代码的默认描述\n * \n * @param code 响应代码\n * @returns 响应代码描述\n */\nexport const getDescription = (code: number): string => {\n  switch (code) {\n    case SUCCESS:\n      return '操作成功';\n    case BAD_REQUEST:\n      return '请求参数错误';\n    case UNAUTHORIZED:\n      return '未认证，需要登录';\n    case FORBIDDEN:\n      return '权限不足';\n    case NOT_FOUND:\n      return '资源不存在';\n    case CONFLICT:\n      return '资源冲突';\n    case UNPROCESSABLE_ENTITY:\n      return '请求语义错误';\n    case TOO_MANY_REQUESTS:\n      return '请求频率过高';\n    case INTERNAL_SERVER_ERROR:\n      return '服务器内部错误';\n    case BAD_GATEWAY:\n      return '网关错误';\n    case SERVICE_UNAVAILABLE:\n      return '服务不可用';\n    case GATEWAY_TIMEOUT:\n      return '网关超时';\n    default:\n      return '未知错误';\n  }\n};\n\n// ============= 响应代码集合 =============\n\n/**\n * 所有成功响应代码\n */\nexport const SUCCESS_CODES = [SUCCESS];\n\n/**\n * 所有客户端错误响应代码\n */\nexport const CLIENT_ERROR_CODES = [\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n];\n\n/**\n * 所有服务器错误响应代码\n */\nexport const SERVER_ERROR_CODES = [\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n];\n\n/**\n * 所有错误响应代码\n */\nexport const ERROR_CODES = [...CLIENT_ERROR_CODES, ...SERVER_ERROR_CODES];\n\n/**\n * 所有响应代码\n */\nexport const ALL_CODES = [...SUCCESS_CODES, ...ERROR_CODES];\n\n// ============= 默认导出 =============\n\n/**\n * 响应代码常量对象\n */\nexport const ResponseCode = {\n  // 成功状态码\n  SUCCESS,\n  \n  // 客户端错误状态码\n  BAD_REQUEST,\n  UNAUTHORIZED,\n  FORBIDDEN,\n  NOT_FOUND,\n  CONFLICT,\n  UNPROCESSABLE_ENTITY,\n  TOO_MANY_REQUESTS,\n  \n  // 服务器错误状态码\n  INTERNAL_SERVER_ERROR,\n  BAD_GATEWAY,\n  SERVICE_UNAVAILABLE,\n  GATEWAY_TIMEOUT,\n  \n  // 工具方法\n  isSuccess,\n  isClientError,\n  isServerError,\n  isError,\n  getDescription,\n  \n  // 代码集合\n  SUCCESS_CODES,\n  CLIENT_ERROR_CODES,\n  SERVER_ERROR_CODES,\n  ERROR_CODES,\n  ALL_CODES,\n} as const;\n\nexport default ResponseCode;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCwBA,WAAW;2BAAX;;gBAoXb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CAtYyD;kDAC5B;iDACE;;;;;;;;;YAexB,MAAM;gBACX;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,qBAAqB,IAAiC,EAAyC;oBAC1G,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAA+B,mBAAmB;oBAExF,wCAAwC;oBACxC,IAAI,CAAC,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GAAG;wBAC1C,IAAA,4BAAc,EAAC;wBACf,MAAM,IAAI,MAAM,SAAS,OAAO;oBAClC;oBAEA,wBAAwB;oBACxB,IAA8C,SAAS,IAAI,CAAC,OAAO;wBACjE,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;4BAC3B,QAAQ,GAAG,CAAC,QAAQ,SAAS,IAAI,CAAC,SAAS;4BAE3C,WAAW;4BACX,QAAQ,GAAG,CAAC,2DAA2D,SAAS,IAAI,CAAC,SAAS,GAAG;4BAEjG,6BAA6B;4BAC7B,WAAW;gCACT,MAAM,YAAY,SAAS,aAAa,CAAC;gCACzC,IAAI,WAAW;oCACb,UAAU,KAAK,GAAG,SAAS,IAAI,CAAC,SAAS;oCACzC,UAAU,aAAa,CAAC,IAAI,MAAM,SAAS;wCAAE,SAAS;oCAAK;oCAC3D,UAAU,aAAa,CAAC,IAAI,MAAM,UAAU;wCAAE,SAAS;oCAAK;oCAC5D,QAAQ,GAAG,CAAC;gCACd;4BACF,GAAG;wBACL,OACE,QAAQ,GAAG,CAAC;;oBAIhB,OAAO,SAAS,IAAI;gBACtB;gBAEA,yBAAyB;gBAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,aAAa,MAAM,IAAkB,EAA0B;oBAC7D,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAgB,eAAe;oBAErE,YAAY;oBACZ,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,aAAa,eAAuC;oBAClD,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,SAAwB;oBACnC,IAAI;wBACF,MAAM,mBAAU,CAAC,IAAI,CAAO;oBAC9B,SAAU;wBACR,uBAAuB;wBACvB,qBAAY,CAAC,UAAU;oBACzB;gBACF;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,gBAAkC;oBAC7C,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAU;wBAC/C,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,OAAO,aAAsB;oBAC3B,OAAO,qBAAY,CAAC,QAAQ;gBAC9B;gBAEA;;;;;;;;;;;;;;GAcC,GACD,OAAO,WAA0B;oBAC/B,OAAO,qBAAY,CAAC,QAAQ;gBAC9B;gBAEA;;;;;;;;;;;GAWC,GACD,OAAO,aAAmB;oBACxB,qBAAY,CAAC,UAAU;gBACzB;gBAEA;;;;;;GAMC,GACD,OAAO,iBAAuB;oBAC5B,oBAAoB;oBACpB,qBAAY,CAAC,UAAU;gBACzB;gBAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,aAAa,WAAW,IAAwB,EAA0B;oBACxE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,qBACA;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,aAAa,WAAW,IAAwB,EAA0B;oBACxE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,qBACA;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,YAA6B;oBACxC,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAS;oBAE/C,UAAU;oBACV,IAAI,SAAS,IAAI,EACf,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI;oBAGrC,OAAO,SAAS,IAAI;gBACtB;gBAEA,8BAA8B;gBAE9B;;;GAGC,GACD,OAAO,kBAA2B;oBAChC,OAAO,YAAY,UAAU;gBAC/B;gBACA;;;GAGC,GACD,aAAa,UAAU,IAAwB,EAA0B;oBACvE,OAAO,YAAY,UAAU,CAAC;gBAChC;gBAEA;;;GAGC,GACD,OAAO,cAAoB;oBACzB,YAAY,UAAU;gBACxB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC5HF,kBAAkB;2BAAlB;;gBAOb,mCAAmC;gBAEnC,OASE;2BATF;;gBAxLa,cAAc;2BAAd;;gBAuIA,SAAS;2BAAT;;gBASA,QAAQ;2BAAR;;gBAWA,gBAAgB;2BAAhB;;gBAtCA,WAAW;2BAAX;;gBASA,WAAW;2BAAX;;;;;yCAzNyB;wCACd;kDAC8B;6CAE1B;;;;;;;;;;sBAOhB;gBACV,iBAAiB;gBAEjB,wBAAwB;gBAExB,sBAAsB;gBAEtB,qBAAqB;gBAErB,YAAY;eATF,qBAAA;YA6BZ;;CAEC,GACD,MAAM,uBAA2D;gBAC/D,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,YAAY,CAAC,EAAE;oBAC3B,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,SAAS,CAAC,EAAE;oBACxB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,QAAQ,CAAC,EAAE;oBACvB,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,oBAAoB,CAAC,EAAE;oBACnC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,iBAAiB,CAAC,EAAE;oBAChC,WAAW;gBACb;gBACA,CAAC,2BAAY,CAAC,qBAAqB,CAAC,EAAE;oBACpC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,WAAW,CAAC,EAAE;oBAC1B,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,mBAAmB,CAAC,EAAE;oBAClC,WAAW;oBACX,eAAe;gBACjB;gBACA,CAAC,2BAAY,CAAC,eAAe,CAAC,EAAE;oBAC9B,WAAW;oBACX,eAAe;gBACjB;YACF;YAUO,MAAM,iBAAiB,CAC5B,UACA;gBAEA,IAAI,CAAC,YAAY,2BAAY,CAAC,SAAS,CAAC,SAAS,IAAI,GACnD;gBAGF,MAAM,YAAY,SAAS,IAAI;gBAC/B,MAAM,eAAe,SAAS,OAAO,IAAI,IAAA,6BAAc,EAAC;gBAExD,OAAO;gBACP,QAAQ,GAAG,CAAC,uBAAuB;oBACjC;oBACA;oBACA;oBACA;gBACF;gBAEA,OAAO;gBACP,MAAM,gBAAgB,oBAAoB,CAAC,UAAU,IAAI;oBACvD,WAAW;gBACb;gBACA,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE,GAAG,MAAM;gBAAC;gBAElD,eAAe;gBACf,MAAM,iBAAiB,YAAY,aAAa,IAAI;gBAEpD,QAAQ,GAAG,CAAC,WAAW;oBACrB;oBACA;oBACA;gBACF;gBAEA,SAAS;gBACT,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC;oBAAE,MAAM;oBAAW,SAAS;oBAAc;gBAAS;gBAGzE,aAAa;gBACb,OAAQ,YAAY,WAAW;oBAC7B;wBACE,QAAQ,GAAG,CAAC;wBACZ;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,OAAO,CAAC;wBAChB;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;oBAEF;wBACE,QAAQ,GAAG,CAAC,SAAS;wBACrB,kBAAY,CAAC,KAAK,CAAC;4BACjB,SAAS;4BACT,aAAa;4BACb,UAAU;wBACZ;wBACA;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW,WAAW;wBAClC,gBAAgB,WAAW;wBAC3B;oBAEF;wBACE,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd;gBACJ;YACF;YAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,WAAmB;gBAC1C,QAAQ,GAAG,CAAC,wBAAwB;oBAAE;oBAAW;gBAAa;gBAE9D,IAAI,cAAc,2BAAY,CAAC,YAAY,EAAE;oBAC3C,qBAAqB;oBACrB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;oBAEzB,QAAQ,GAAG,CAAC,YAAY;wBAAE;wBAAa;oBAAmB;oBAE1D,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb;oBACF;oBAEA,gBAAgB;oBAChB,qBAAW,CAAC,WAAW;oBACvB,QAAQ,GAAG,CAAC,oBAAoB;oBAChC,aAAO,CAAC,KAAK,CAAC,gBAAgB;oBAC9B,YAAO,CAAC,IAAI,CAAC;gBACf,OAAO,IAAI,cAAc,2BAAY,CAAC,SAAS,EAAE;oBAC/C,QAAQ,GAAG,CAAC,cAAc;oBAC1B,aAAO,CAAC,KAAK,CAAC,gBAAgB;gBAChC;YACF;YASO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,cAAc,CAAC;gBAC1B,aAAO,CAAC,OAAO,CAAC;YAClB;YAOO,MAAM,YAAY,CAAC;gBACxB,aAAO,CAAC,KAAK,CAAC;YAChB;YAOO,MAAM,WAAW,CAAC;gBACvB,aAAO,CAAC,IAAI,CAAC;YACf;YASO,MAAM,mBAAmB,CAC9B,OACA,aACA,OAAiD,MAAM;gBAEvD,kBAAY,CAAC,KAAK,CAAC;oBACjB,SAAS;oBACT;oBACA,UAAU;gBACZ;YACF;YAUO,MAAM,qBAAqB,CAAC;gBACjC,OAAO,CAAC,UAA4B;oBAClC,MAAM,cAAc;wBAAE,GAAG,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAClD,eAAe,UAAU;gBAC3B;YACF;gBAIA,WAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCnGa,SAAS;2BAAT;;gBAnIA,WAAW;2BAAX;;gBA1CA,WAAW;2BAAX;;gBAiJA,kBAAkB;2BAAlB;;gBA7HA,QAAQ;2BAAR;;gBAoJA,WAAW;2BAAX;;gBA9JA,SAAS;2BAAT;;gBA0CA,eAAe;2BAAf;;gBAfA,qBAAqB;2BAArB;;gBAtBA,SAAS;2BAAT;;gBAqKA,YAAY;2BAAZ;;gBAtBA,kBAAkB;2BAAlB;;gBA/GA,mBAAmB;2BAAnB;;gBAtDA,OAAO;2BAAP;;gBAmJA,aAAa;2BAAb;;gBA9GA,iBAAiB;2BAAjB;;gBAzBA,YAAY;2BAAZ;;gBAoBA,oBAAoB;2BAApB;;gBA6Lb,OAA4B;2BAA5B;;gBA9Ga,cAAc;2BAAd;;gBAhCA,aAAa;2BAAb;;gBAoBA,OAAO;2BAAP;;gBAVA,aAAa;2BAAb;;gBApBA,SAAS;2BAAT;;;;;;;;;;;;;YArEN,MAAM,UAAU;YAOhB,MAAM,cAAc;YAKpB,MAAM,eAAe;YAKrB,MAAM,YAAY;YAKlB,MAAM,YAAY;YAKlB,MAAM,WAAW;YAKjB,MAAM,uBAAuB;YAK7B,MAAM,oBAAoB;YAO1B,MAAM,wBAAwB;YAK9B,MAAM,cAAc;YAKpB,MAAM,sBAAsB;YAK5B,MAAM,kBAAkB;YAUxB,MAAM,YAAY,CAAC;gBACxB,OAAO,SAAS;YAClB;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,gBAAgB,CAAC;gBAC5B,OAAO,QAAQ,OAAO,OAAO;YAC/B;YAQO,MAAM,UAAU,CAAC;gBACtB,OAAO,CAAC,UAAU;YACpB;YAUO,MAAM,iBAAiB,CAAC;gBAC7B,OAAQ;oBACN,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT,KAAK;wBACH,OAAO;oBACT;wBACE,OAAO;gBACX;YACF;YAOO,MAAM,gBAAgB;gBAAC;aAAQ;YAK/B,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,qBAAqB;gBAChC;gBACA;gBACA;gBACA;aACD;YAKM,MAAM,cAAc;mBAAI;mBAAuB;aAAmB;YAKlE,MAAM,YAAY;mBAAI;mBAAkB;aAAY;YAOpD,MAAM,eAAe;gBAC1B,QAAQ;gBACR;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBAEA,OAAO;gBACP;gBACA;gBACA;gBACA;gBACA;YACF;gBAEA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IHzOD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,kCAAiC;YAAC;SAAiC;IAAA;;AACn9B"}